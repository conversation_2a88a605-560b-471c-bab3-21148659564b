# YOLO全能检测系统依赖包
# 注意：PyTorch需要根据CUDA版本安装对应的GPU版本

ultralytics>=8.0.0
PySide6>=6.5.0
opencv-python>=4.8.0
numpy>=1.24.0
Pillow>=9.5.0
PyYAML>=6.0
matplotlib>=3.7.0
seaborn>=0.12.0
pandas>=2.0.0
tqdm>=4.65.0
psutil>=5.9.0

# PyTorch GPU版本 (CUDA 12.4)
# 如果需要安装GPU版本，请使用以下命令：
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

# PyTorch CPU版本（备用）
# torch>=2.0.0
# torchvision>=0.15.0
# torchaudio>=2.0.0
