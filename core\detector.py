"""
YOLO检测器核心模块
支持所有YOLO版本和检测任务
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path
import torch
from ultralytics import YOLO
from ultralytics.engine.results import Results

from .config_manager import ConfigManager
from .utils import PerformanceMonitor


class YOLODetector:
    """YOLO检测器类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化检测器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.model = None
        self.model_path = None
        self.device = None
        self.performance_monitor = PerformanceMonitor()
        
        # 支持的任务类型
        self.task_types = {
            'detect': '目标检测',
            'segment': '实例分割', 
            'pose': '姿态估计',
            'classify': '图像分类',
            'obb': '有向边界框检测'
        }
        
        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化模型"""
        model_path = self.config_manager.get('model.default_model', 'yolo11n.pt')
        self.load_model(model_path)
    
    def load_model(self, model_path: str) -> bool:
        """
        加载YOLO模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            # 检查模型文件是否存在
            if not Path(model_path).exists() and not model_path.startswith(('yolo', 'rtdetr')):
                print(f"模型文件不存在: {model_path}")
                return False
            
            # 加载模型
            self.model = YOLO(model_path)
            self.model_path = model_path
            
            # 设置设备
            device = self.config_manager.get('detection.device', 'auto')
            if device == 'auto':
                self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
            else:
                self.device = device
            
            # 将模型移动到指定设备
            self.model.to(self.device)
            
            print(f"模型加载成功: {model_path}")
            print(f"使用设备: {self.device}")
            print(f"模型任务类型: {self.get_task_type()}")
            
            return True
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def get_task_type(self) -> str:
        """
        获取模型任务类型
        
        Returns:
            任务类型
        """
        if self.model is None:
            return 'unknown'
        
        try:
            task = self.model.task
            return self.task_types.get(task, task)
        except:
            return 'unknown'
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        try:
            return {
                'model_path': self.model_path,
                'task': self.model.task,
                'task_name': self.get_task_type(),
                'device': self.device,
                'names': self.model.names if hasattr(self.model, 'names') else {},
                'nc': len(self.model.names) if hasattr(self.model, 'names') else 0
            }
        except:
            return {'model_path': self.model_path}
    
    def predict(self, source: Union[str, np.ndarray], **kwargs) -> Optional[List[Results]]:
        """
        执行预测
        
        Args:
            source: 输入源（图像路径、图像数组、视频路径等）
            **kwargs: 其他预测参数
            
        Returns:
            预测结果列表
        """
        if self.model is None:
            print("模型未加载")
            return None
        
        try:
            self.performance_monitor.start_frame()
            
            # 获取预测参数
            predict_params = self._get_predict_params(**kwargs)
            
            # 执行预测
            results = self.model.predict(source, **predict_params)
            
            self.performance_monitor.end_frame()
            
            return results
            
        except Exception as e:
            print(f"预测失败: {e}")
            return None
    
    def track(self, source: Union[str, np.ndarray], **kwargs) -> Optional[List[Results]]:
        """
        执行跟踪检测
        
        Args:
            source: 输入源
            **kwargs: 其他跟踪参数
            
        Returns:
            跟踪结果列表
        """
        if self.model is None:
            print("模型未加载")
            return None
        
        try:
            self.performance_monitor.start_frame()
            
            # 获取跟踪参数
            track_params = self._get_track_params(**kwargs)
            
            # 执行跟踪
            results = self.model.track(source, **track_params)
            
            self.performance_monitor.end_frame()
            
            return results
            
        except Exception as e:
            print(f"跟踪失败: {e}")
            return None
    
    def _get_predict_params(self, **kwargs) -> Dict[str, Any]:
        """
        获取预测参数
        
        Returns:
            预测参数字典
        """
        params = {
            'conf': kwargs.get('conf', self.config_manager.get('detection.conf', 0.25)),
            'iou': kwargs.get('iou', self.config_manager.get('detection.iou', 0.45)),
            'max_det': kwargs.get('max_det', self.config_manager.get('detection.max_det', 1000)),
            'imgsz': kwargs.get('imgsz', self.config_manager.get('detection.imgsz', 640)),
            'device': kwargs.get('device', self.device),
            'half': kwargs.get('half', self.config_manager.get('detection.half', False)),
            'augment': kwargs.get('augment', self.config_manager.get('detection.augment', False)),
            'visualize': kwargs.get('visualize', self.config_manager.get('detection.visualize', False)),
            'save': kwargs.get('save', self.config_manager.get('detection.save', False)),
            'save_conf': kwargs.get('save_conf', self.config_manager.get('detection.save_conf', False)),
            'save_txt': kwargs.get('save_txt', self.config_manager.get('detection.save_txt', False)),
            'save_crop': kwargs.get('save_crop', self.config_manager.get('detection.save_crop', False)),
            'show_labels': kwargs.get('show_labels', self.config_manager.get('detection.show_labels', True)),
            'show_conf': kwargs.get('show_conf', self.config_manager.get('detection.show_conf', True)),
            'show_boxes': kwargs.get('show_boxes', self.config_manager.get('detection.show_boxes', True)),
            'line_width': kwargs.get('line_width', self.config_manager.get('detection.line_width', 3)),
            'verbose': kwargs.get('verbose', False)
        }
        
        # 移除None值
        return {k: v for k, v in params.items() if v is not None}
    
    def _get_track_params(self, **kwargs) -> Dict[str, Any]:
        """
        获取跟踪参数
        
        Returns:
            跟踪参数字典
        """
        # 获取基础预测参数
        params = self._get_predict_params(**kwargs)
        
        # 添加跟踪特定参数
        tracker = kwargs.get('tracker', self.config_manager.get('tracking.tracker', 'botsort.yaml'))
        persist = kwargs.get('persist', self.config_manager.get('tracking.persist', True))
        
        params.update({
            'tracker': tracker,
            'persist': persist
        })
        
        return params
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计字典
        """
        return self.performance_monitor.get_stats()
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_monitor = PerformanceMonitor()
    
    def is_loaded(self) -> bool:
        """
        检查模型是否已加载
        
        Returns:
            是否已加载
        """
        return self.model is not None
    
    def get_supported_models(self) -> List[str]:
        """
        获取支持的模型列表
        
        Returns:
            支持的模型列表
        """
        return self.config_manager.get('model.supported_models', [])
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新字典
        """
        self.config_manager.update(config_updates)
    
    def export_model(self, format: str = 'onnx', **kwargs) -> bool:
        """
        导出模型
        
        Args:
            format: 导出格式
            **kwargs: 其他导出参数
            
        Returns:
            是否导出成功
        """
        if self.model is None:
            print("模型未加载")
            return False
        
        try:
            self.model.export(format=format, **kwargs)
            print(f"模型导出成功，格式: {format}")
            return True
        except Exception as e:
            print(f"模型导出失败: {e}")
            return False
