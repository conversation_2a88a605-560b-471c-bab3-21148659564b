"""
主窗口模块
YOLO检测系统的主界面
"""

import sys
import cv2
import numpy as np
from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QSplitter, QMenuBar, QStatusBar, QToolBar,
                               QMessageBox, QFileDialog, QProgressBar,
                               QLabel, QPushButton, QApplication)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QSize
from PySide6.QtGui import QIcon, QKeySequence, QPixmap, QAction
from typing import Optional, Dict, Any

from core.config_manager import ConfigManager
from core.detector import YOLODetector
from core.tracker import TrackingManager
from core.utils import is_image_file, is_video_file, get_video_info, create_output_directory
from .widgets.video_widget import VideoDisplayWidget
from .widgets.control_panel import ControlPanel
from .widgets.result_panel import ResultPanel


class DetectionThread(QThread):
    """检测线程"""
    
    # 信号定义
    frame_processed = Signal(np.ndarray, float, str)  # 处理完成的帧
    detection_results = Signal(object)  # 检测结果
    tracking_results = Signal(dict, dict)  # 跟踪结果
    error_occurred = Signal(str)  # 错误信号
    finished = Signal()  # 完成信号
    
    def __init__(self, detector: YOLODetector, tracking_manager: TrackingManager):
        super().__init__()
        self.detector = detector
        self.tracking_manager = tracking_manager
        self.source = None
        self.is_running = False
        self.tracking_enabled = False
        
    def set_source(self, source, tracking_enabled: bool = False):
        """设置输入源"""
        self.source = source
        self.tracking_enabled = tracking_enabled
    
    def run(self):
        """运行检测"""
        if not self.source:
            return
        
        self.is_running = True
        
        try:
            # 打开视频源
            if isinstance(self.source, str):
                if is_image_file(self.source):
                    # 处理单张图像
                    self._process_image()
                    return
                else:
                    # 处理视频或摄像头
                    cap = cv2.VideoCapture(self.source)
            elif isinstance(self.source, int):
                # 摄像头
                cap = cv2.VideoCapture(self.source)
            else:
                self.error_occurred.emit("不支持的输入源类型")
                return
            
            if not cap.isOpened():
                self.error_occurred.emit("无法打开输入源")
                return
            
            # 处理视频流
            self._process_video_stream(cap)
            
        except Exception as e:
            self.error_occurred.emit(f"检测过程中发生错误: {str(e)}")
        finally:
            self.is_running = False
            self.finished.emit()
    
    def _process_image(self):
        """处理单张图像"""
        try:
            if self.tracking_enabled:
                results = self.detector.track(self.source)
            else:
                results = self.detector.predict(self.source)
            
            if results:
                # 读取图像用于显示
                image = cv2.imread(self.source)
                if image is not None:
                    # 绘制结果
                    annotated_image = results[0].plot()
                    
                    # 发送信号
                    self.frame_processed.emit(annotated_image, 0.0, "图像处理完成")
                    self.detection_results.emit(results)
                    
                    if self.tracking_enabled:
                        self.tracking_manager.update_tracks(results)
                        stats = self.tracking_manager.get_track_statistics()
                        tracks_info = self.tracking_manager.get_all_tracks_info()
                        self.tracking_results.emit(stats, tracks_info)
            
        except Exception as e:
            self.error_occurred.emit(f"图像处理失败: {str(e)}")
    
    def _process_video_stream(self, cap):
        """处理视频流"""
        frame_count = 0
        
        while self.is_running and cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            try:
                # 执行检测或跟踪
                if self.tracking_enabled:
                    results = self.detector.track(frame)
                else:
                    results = self.detector.predict(frame)
                
                if results:
                    # 绘制结果
                    annotated_frame = results[0].plot()
                    
                    # 获取性能统计
                    stats = self.detector.get_performance_stats()
                    fps = stats.get('fps', 0.0)
                    
                    # 创建信息文本
                    info_text = f"帧数: {frame_count}"
                    if self.tracking_enabled:
                        info_text += f"\n跟踪模式"
                    
                    # 发送信号
                    self.frame_processed.emit(annotated_frame, fps, info_text)
                    self.detection_results.emit(results)
                    
                    # 更新跟踪信息
                    if self.tracking_enabled:
                        self.tracking_manager.update_tracks(results)
                        tracking_stats = self.tracking_manager.get_track_statistics()
                        tracks_info = self.tracking_manager.get_all_tracks_info()
                        self.tracking_results.emit(tracking_stats, tracks_info)
                
                # 控制帧率
                self.msleep(30)  # 约30FPS
                
            except Exception as e:
                self.error_occurred.emit(f"帧处理失败: {str(e)}")
                break
        
        cap.release()
    
    def stop(self):
        """停止检测"""
        self.is_running = False


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.detector = YOLODetector(self.config_manager)
        self.tracking_manager = TrackingManager(self.config_manager)
        
        # 检测线程
        self.detection_thread = None
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        self.setup_connections()
        
        # 加载配置
        self.load_initial_config()
    
    def setup_window(self):
        """设置窗口属性"""
        config = self.config_manager.get('ui', {})
        title = config.get('title', 'YOLO全能检测系统')
        window_size = config.get('window_size', [1400, 900])
        
        self.setWindowTitle(title)
        self.setMinimumSize(1200, 800)
        self.resize(window_size[0], window_size[1])
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        self.control_panel = ControlPanel(self.config_manager)
        self.control_panel.setMaximumWidth(400)
        self.control_panel.setMinimumWidth(300)
        
        # 中间视频显示区域
        self.video_widget = VideoDisplayWidget()
        
        # 右侧结果面板
        self.result_panel = ResultPanel()
        self.result_panel.setMaximumWidth(400)
        self.result_panel.setMinimumWidth(300)
        
        # 添加到分割器
        main_splitter.addWidget(self.control_panel)
        main_splitter.addWidget(self.video_widget)
        main_splitter.addWidget(self.result_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([300, 800, 300])
        
        main_layout.addWidget(main_splitter)
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        open_action = QAction("打开文件(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        # 保存结果
        save_action = QAction("保存结果(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_results)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 模型管理
        model_action = QAction("模型管理(&M)", self)
        model_action.triggered.connect(self.open_model_manager)
        tools_menu.addAction(model_action)
        
        # 配置管理
        config_action = QAction("配置管理(&C)", self)
        config_action.triggered.connect(self.open_config_manager)
        tools_menu.addAction(config_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 开始检测按钮
        self.start_action = QAction("开始检测", self)
        self.start_action.triggered.connect(self.start_detection)
        toolbar.addAction(self.start_action)
        
        # 停止检测按钮
        self.stop_action = QAction("停止检测", self)
        self.stop_action.triggered.connect(self.stop_detection)
        self.stop_action.setEnabled(False)
        toolbar.addAction(self.stop_action)
        
        toolbar.addSeparator()
        
        # 截图按钮
        screenshot_action = QAction("截图", self)
        screenshot_action.triggered.connect(self.take_screenshot)
        toolbar.addAction(screenshot_action)
        
        # 录制按钮
        record_action = QAction("录制", self)
        record_action.triggered.connect(self.toggle_recording)
        toolbar.addAction(record_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 模型信息标签
        self.model_status_label = QLabel("模型: 未加载")
        self.status_bar.addPermanentWidget(self.model_status_label)

    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.model_changed.connect(self.on_model_changed)
        self.control_panel.config_changed.connect(self.on_config_changed)
        self.control_panel.source_changed.connect(self.on_source_changed)

        # 结果面板信号
        self.result_panel.export_requested.connect(self.export_results)

        # 视频组件信号
        self.video_widget.frame_clicked.connect(self.on_frame_clicked)

    def load_initial_config(self):
        """加载初始配置"""
        # 加载默认模型
        default_model = self.config_manager.get('model.default_model', 'yolo11n.pt')
        if self.detector.load_model(default_model):
            model_info = self.detector.get_model_info()
            self.result_panel.update_model_info(model_info)
            self.model_status_label.setText(f"模型: {model_info.get('model_path', '未知')}")
            self.result_panel.add_log_message(f"模型加载成功: {default_model}", "SUCCESS")
        else:
            self.result_panel.add_log_message(f"模型加载失败: {default_model}", "ERROR")

    def on_model_changed(self, model_path: str):
        """模型改变事件"""
        self.result_panel.add_log_message(f"正在加载模型: {model_path}", "INFO")

        if self.detector.load_model(model_path):
            model_info = self.detector.get_model_info()
            self.result_panel.update_model_info(model_info)
            self.model_status_label.setText(f"模型: {model_info.get('model_path', '未知')}")
            self.result_panel.add_log_message(f"模型加载成功: {model_path}", "SUCCESS")
        else:
            self.result_panel.add_log_message(f"模型加载失败: {model_path}", "ERROR")
            QMessageBox.warning(self, "错误", f"无法加载模型: {model_path}")

    def on_config_changed(self, config: Dict[str, Any]):
        """配置改变事件"""
        # 更新检测器配置
        self.detector.update_config(config)

        # 更新跟踪管理器配置
        if 'tracking.track_history' in config:
            self.tracking_manager.set_history_length(config['tracking.track_history'])

        self.result_panel.add_log_message("配置已更新", "INFO")

    def on_source_changed(self, source_config: Dict[str, Any]):
        """输入源改变事件"""
        source_type = source_config.get('type', 'unknown')

        if source_type == 'camera':
            camera_id = source_config.get('camera_id', 0)
            self.result_panel.update_source_info(f"摄像头 {camera_id}")
        elif source_type == 'video':
            video_path = source_config.get('video_path', '')
            self.result_panel.update_source_info(f"视频文件: {video_path}")
        elif source_type == 'image':
            image_path = source_config.get('image_path', '')
            self.result_panel.update_source_info(f"图像文件: {image_path}")
        elif source_type == 'rtsp':
            rtsp_url = source_config.get('rtsp_url', '')
            self.result_panel.update_source_info(f"RTSP流: {rtsp_url}")

    def on_frame_clicked(self, x: int, y: int):
        """帧点击事件"""
        self.result_panel.add_log_message(f"点击位置: ({x}, {y})", "INFO")

    def start_detection(self):
        """开始检测"""
        if not self.detector.is_loaded():
            QMessageBox.warning(self, "错误", "请先加载模型")
            return

        # 获取输入源配置
        source_type = self.config_manager.get('source.type', 'camera')
        source = None

        if source_type == 'camera':
            source = self.config_manager.get('source.camera_id', 0)
        elif source_type == 'video':
            source = self.config_manager.get('source.video_path', '')
            if not source:
                QMessageBox.warning(self, "错误", "请选择视频文件")
                return
        elif source_type == 'image':
            source = self.config_manager.get('source.image_path', '')
            if not source:
                QMessageBox.warning(self, "错误", "请选择图像文件")
                return
        elif source_type == 'rtsp':
            source = self.config_manager.get('source.rtsp_url', '')
            if not source:
                QMessageBox.warning(self, "错误", "请输入RTSP地址")
                return

        # 检查跟踪是否启用
        tracking_enabled = self.config_manager.get('tracking.enabled', False)

        # 创建并启动检测线程
        self.detection_thread = DetectionThread(self.detector, self.tracking_manager)
        self.detection_thread.set_source(source, tracking_enabled)

        # 连接信号
        self.detection_thread.frame_processed.connect(self.on_frame_processed)
        self.detection_thread.detection_results.connect(self.on_detection_results)
        self.detection_thread.tracking_results.connect(self.on_tracking_results)
        self.detection_thread.error_occurred.connect(self.on_detection_error)
        self.detection_thread.finished.connect(self.on_detection_finished)

        # 启动线程
        self.detection_thread.start()

        # 更新UI状态
        self.start_action.setEnabled(False)
        self.stop_action.setEnabled(True)
        self.result_panel.update_status("检测中", "#00ff00")
        self.status_label.setText("检测中...")

        # 清空跟踪历史
        self.tracking_manager.clear_tracks()

        self.result_panel.add_log_message("开始检测", "SUCCESS")

    def stop_detection(self):
        """停止检测"""
        if self.detection_thread and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait(3000)  # 等待3秒

            if self.detection_thread.isRunning():
                self.detection_thread.terminate()
                self.detection_thread.wait()

        # 更新UI状态
        self.start_action.setEnabled(True)
        self.stop_action.setEnabled(False)
        self.result_panel.update_status("已停止", "#ff4444")
        self.status_label.setText("就绪")

        self.result_panel.add_log_message("检测已停止", "WARNING")

    def on_frame_processed(self, frame: np.ndarray, fps: float, info: str):
        """处理完成的帧"""
        # 更新视频显示
        self.video_widget.update_frame(frame, fps, info)

        # 更新分辨率信息
        h, w = frame.shape[:2]
        self.result_panel.update_resolution(w, h)

    def on_detection_results(self, results):
        """检测结果"""
        # 更新检测结果显示
        self.result_panel.update_detection_results(results)

        # 更新性能统计
        stats = self.detector.get_performance_stats()
        self.result_panel.update_performance_stats(stats)

    def on_tracking_results(self, tracking_stats: Dict[str, Any], tracks_info: Dict[int, Dict[str, Any]]):
        """跟踪结果"""
        # 更新跟踪结果显示
        self.result_panel.update_tracking_results(tracking_stats, tracks_info)

    def on_detection_error(self, error_message: str):
        """检测错误"""
        self.result_panel.add_log_message(error_message, "ERROR")
        QMessageBox.critical(self, "检测错误", error_message)
        self.stop_detection()

    def on_detection_finished(self):
        """检测完成"""
        self.result_panel.add_log_message("检测完成", "INFO")
        self.stop_detection()

    def open_file(self):
        """打开文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "",
            "所有支持的文件 (*.jpg *.jpeg *.png *.bmp *.mp4 *.avi *.mov *.mkv);;图像文件 (*.jpg *.jpeg *.png *.bmp);;视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
        )

        if file_path:
            if is_image_file(file_path):
                self.config_manager.set('source.type', 'image')
                self.config_manager.set('source.image_path', file_path)
            elif is_video_file(file_path):
                self.config_manager.set('source.type', 'video')
                self.config_manager.set('source.video_path', file_path)

            # 更新控制面板
            self.control_panel.load_config()
            self.result_panel.add_log_message(f"已选择文件: {file_path}", "INFO")

    def save_results(self):
        """保存结果"""
        # 这里可以实现保存检测结果的功能
        self.result_panel.add_log_message("保存结果功能待实现", "INFO")

    def take_screenshot(self):
        """截图"""
        if hasattr(self.video_widget, 'current_frame') and self.video_widget.current_frame is not None:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存截图", "", "图像文件 (*.jpg *.png);;所有文件 (*)"
            )
            if file_path:
                if self.video_widget.save_current_frame(file_path):
                    self.result_panel.add_log_message(f"截图已保存: {file_path}", "SUCCESS")
                else:
                    self.result_panel.add_log_message("截图保存失败", "ERROR")
        else:
            QMessageBox.information(self, "提示", "当前没有可截图的画面")

    def toggle_recording(self):
        """切换录制状态"""
        # 录制功能待实现
        self.result_panel.add_log_message("录制功能待实现", "INFO")

    def export_results(self, export_type: str = "results"):
        """导出结果"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", "", "JSON文件 (*.json);;CSV文件 (*.csv);;所有文件 (*)"
        )
        if file_path:
            # 导出跟踪数据
            if self.tracking_manager.export_tracks(file_path):
                self.result_panel.add_log_message(f"结果已导出: {file_path}", "SUCCESS")
            else:
                self.result_panel.add_log_message("结果导出失败", "ERROR")

    def open_model_manager(self):
        """打开模型管理器"""
        # 模型管理器待实现
        QMessageBox.information(self, "提示", "模型管理器功能待实现")

    def open_config_manager(self):
        """打开配置管理器"""
        # 配置管理器待实现
        QMessageBox.information(self, "提示", "配置管理器功能待实现")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         "YOLO全能检测系统\n\n"
                         "基于PySide6和Ultralytics YOLO开发\n"
                         "支持所有YOLO版本和检测任务\n"
                         "支持目标跟踪和高度自定义配置\n\n"
                         "版本: 1.0.0")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止检测线程
        if self.detection_thread and self.detection_thread.isRunning():
            self.stop_detection()

        # 保存配置
        if self.config_manager.get('ui.auto_save', True):
            self.config_manager.save_config()

        event.accept()
