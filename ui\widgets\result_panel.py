"""
结果显示面板
显示检测和跟踪结果信息
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                               QLabel, QTextEdit, QTableWidget, QTableWidgetItem,
                               QTabWidget, QProgressBar, QPushButton,
                               QHeaderView, QAbstractItemView)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QColor
from typing import Dict, Any
import time
import psutil
import torch


class ResultPanel(QWidget):
    """结果显示面板"""
    
    # 信号定义
    export_requested = Signal(str)  # 导出请求信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
        # 数据存储
        self.detection_results = []
        self.tracking_results = []
        self.performance_data = {}
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次

        # 性能监控数据
        self.performance_history = []
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 实时信息标签页
        self.info_tab = self.create_info_tab()
        self.tab_widget.addTab(self.info_tab, "实时信息")
        
        # 检测结果标签页
        self.detection_tab = self.create_detection_tab()
        self.tab_widget.addTab(self.detection_tab, "检测结果")
        
        # 跟踪结果标签页
        self.tracking_tab = self.create_tracking_tab()
        self.tab_widget.addTab(self.tracking_tab, "跟踪结果")
        
        # 性能监控标签页
        self.performance_tab = self.create_performance_tab()
        self.tab_widget.addTab(self.performance_tab, "性能监控")
        
        layout.addWidget(self.tab_widget)
        
        # 控制按钮
        self.create_control_buttons(layout)
    
    def create_info_tab(self) -> QWidget:
        """创建实时信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        info_group = QGroupBox("基本信息")
        info_layout = QVBoxLayout(info_group)
        
        # 状态信息
        self.status_label = QLabel("状态: 等待中")
        self.status_label.setStyleSheet("QLabel { color: #888; font-weight: bold; }")
        info_layout.addWidget(self.status_label)
        
        # 模型信息
        self.model_label = QLabel("模型: 未加载")
        info_layout.addWidget(self.model_label)
        
        # 输入源信息
        self.source_label = QLabel("输入源: 未设置")
        info_layout.addWidget(self.source_label)
        
        # 分辨率信息
        self.resolution_label = QLabel("分辨率: -")
        info_layout.addWidget(self.resolution_label)
        
        layout.addWidget(info_group)
        
        # 实时统计组
        stats_group = QGroupBox("实时统计")
        stats_layout = QVBoxLayout(stats_group)
        
        # FPS显示
        self.fps_label = QLabel("FPS: 0.0")
        self.fps_label.setStyleSheet("QLabel { color: #0a84ff; font-size: 14px; font-weight: bold; }")
        stats_layout.addWidget(self.fps_label)
        
        # 检测数量
        self.detection_count_label = QLabel("检测目标: 0")
        stats_layout.addWidget(self.detection_count_label)
        
        # 跟踪数量
        self.tracking_count_label = QLabel("跟踪目标: 0")
        stats_layout.addWidget(self.tracking_count_label)
        
        # 处理时间
        self.processing_time_label = QLabel("处理时间: 0ms")
        stats_layout.addWidget(self.processing_time_label)
        
        layout.addWidget(stats_group)
        
        # 日志显示
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        layout.addStretch()
        
        return widget
    
    def create_detection_tab(self) -> QWidget:
        """创建检测结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 检测结果表格
        self.detection_table = QTableWidget()
        self.detection_table.setColumnCount(6)
        self.detection_table.setHorizontalHeaderLabels([
            "类别", "置信度", "X1", "Y1", "X2", "Y2"
        ])
        
        # 设置表格属性
        header = self.detection_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.detection_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.detection_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.detection_table)
        
        return widget
    
    def create_tracking_tab(self) -> QWidget:
        """创建跟踪结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 跟踪统计信息
        stats_group = QGroupBox("跟踪统计")
        stats_layout = QHBoxLayout(stats_group)
        
        self.active_tracks_label = QLabel("活跃轨迹: 0")
        self.total_tracks_label = QLabel("总轨迹数: 0")
        self.avg_trail_length_label = QLabel("平均轨迹长度: 0")
        
        stats_layout.addWidget(self.active_tracks_label)
        stats_layout.addWidget(self.total_tracks_label)
        stats_layout.addWidget(self.avg_trail_length_label)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
        # 跟踪结果表格
        self.tracking_table = QTableWidget()
        self.tracking_table.setColumnCount(7)
        self.tracking_table.setHorizontalHeaderLabels([
            "跟踪ID", "类别", "置信度", "中心X", "中心Y", "轨迹长度", "最后更新"
        ])
        
        # 设置表格属性
        header = self.tracking_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.tracking_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tracking_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.tracking_table)
        
        return widget
    
    def create_performance_tab(self) -> QWidget:
        """创建性能监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性能指标组
        metrics_group = QGroupBox("性能指标")
        metrics_layout = QVBoxLayout(metrics_group)
        
        # CPU使用率
        cpu_layout = QHBoxLayout()
        cpu_layout.addWidget(QLabel("CPU使用率:"))
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        self.cpu_label = QLabel("0%")
        cpu_layout.addWidget(self.cpu_progress)
        cpu_layout.addWidget(self.cpu_label)
        metrics_layout.addLayout(cpu_layout)
        
        # 内存使用率
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("内存使用率:"))
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_label = QLabel("0%")
        memory_layout.addWidget(self.memory_progress)
        memory_layout.addWidget(self.memory_label)
        metrics_layout.addLayout(memory_layout)
        
        # GPU使用率（如果可用）
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("GPU使用率:"))
        self.gpu_progress = QProgressBar()
        self.gpu_progress.setRange(0, 100)
        self.gpu_label = QLabel("N/A")
        gpu_layout.addWidget(self.gpu_progress)
        gpu_layout.addWidget(self.gpu_label)
        metrics_layout.addLayout(gpu_layout)
        
        layout.addWidget(metrics_group)
        
        # 性能历史
        history_group = QGroupBox("性能历史")
        history_layout = QVBoxLayout(history_group)
        
        self.performance_text = QTextEdit()
        self.performance_text.setMaximumHeight(200)
        self.performance_text.setReadOnly(True)
        history_layout.addWidget(self.performance_text)
        
        layout.addWidget(history_group)
        layout.addStretch()
        
        return widget
    
    def create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 清空结果按钮
        clear_btn = QPushButton("清空结果")
        clear_btn.clicked.connect(self.clear_results)
        button_layout.addWidget(clear_btn)
        
        # 导出结果按钮
        export_btn = QPushButton("导出结果")
        export_btn.clicked.connect(self.export_results)
        button_layout.addWidget(export_btn)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_display)
        button_layout.addWidget(refresh_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def update_status(self, status: str, color: str = "#888"):
        """更新状态信息"""
        self.status_label.setText(f"状态: {status}")
        self.status_label.setStyleSheet(f"QLabel {{ color: {color}; font-weight: bold; }}")
    
    def update_model_info(self, model_info: Dict[str, Any]):
        """更新模型信息"""
        model_name = model_info.get('model_path', '未知')
        task_name = model_info.get('task_name', '未知')
        device = model_info.get('device', '未知')
        
        self.model_label.setText(f"模型: {model_name} ({task_name}) - {device}")
    
    def update_source_info(self, source_info: str):
        """更新输入源信息"""
        self.source_label.setText(f"输入源: {source_info}")
    
    def update_resolution(self, width: int, height: int):
        """更新分辨率信息"""
        self.resolution_label.setText(f"分辨率: {width}x{height}")
    
    def update_performance_stats(self, stats: Dict[str, Any]):
        """更新性能统计"""
        fps = stats.get('fps', 0.0)
        processing_time = stats.get('avg_processing_time', 0.0) * 1000  # 转换为毫秒
        
        self.fps_label.setText(f"FPS: {fps:.1f}")
        self.processing_time_label.setText(f"处理时间: {processing_time:.1f}ms")
        
        # 更新性能数据
        self.performance_data = stats
    
    def update_detection_results(self, results):
        """更新检测结果"""
        if not results:
            return
        
        # 清空表格
        self.detection_table.setRowCount(0)
        
        # 添加检测结果
        for result in results:
            if hasattr(result, 'boxes') and result.boxes is not None:
                boxes = result.boxes
                if len(boxes) > 0:
                    self.detection_table.setRowCount(len(boxes))
                    
                    for i, box in enumerate(boxes):
                        # 获取检测信息
                        cls = int(box.cls.item()) if hasattr(box, 'cls') else 0
                        conf = float(box.conf.item()) if hasattr(box, 'conf') else 0.0
                        xyxy = box.xyxy[0].tolist() if hasattr(box, 'xyxy') else [0, 0, 0, 0]
                        
                        # 获取类别名称
                        class_name = result.names.get(cls, f"Class_{cls}") if hasattr(result, 'names') else f"Class_{cls}"
                        
                        # 填充表格
                        self.detection_table.setItem(i, 0, QTableWidgetItem(class_name))
                        self.detection_table.setItem(i, 1, QTableWidgetItem(f"{conf:.3f}"))
                        self.detection_table.setItem(i, 2, QTableWidgetItem(f"{xyxy[0]:.1f}"))
                        self.detection_table.setItem(i, 3, QTableWidgetItem(f"{xyxy[1]:.1f}"))
                        self.detection_table.setItem(i, 4, QTableWidgetItem(f"{xyxy[2]:.1f}"))
                        self.detection_table.setItem(i, 5, QTableWidgetItem(f"{xyxy[3]:.1f}"))
        
        # 更新检测数量
        total_detections = sum(len(result.boxes) if hasattr(result, 'boxes') and result.boxes is not None else 0 for result in results)
        self.detection_count_label.setText(f"检测目标: {total_detections}")
    
    def update_tracking_results(self, tracking_stats: Dict[str, Any], tracks_info: Dict[int, Dict[str, Any]]):
        """更新跟踪结果"""
        # 更新跟踪统计
        active_tracks = tracking_stats.get('active_tracks', 0)
        total_tracks = tracking_stats.get('total_tracks_seen', 0)
        avg_trail_length = tracking_stats.get('avg_trail_length', 0.0)

        self.active_tracks_label.setText(f"活跃轨迹: {active_tracks}")
        self.total_tracks_label.setText(f"总轨迹数: {total_tracks}")
        self.avg_trail_length_label.setText(f"平均轨迹长度: {avg_trail_length:.1f}")
        self.tracking_count_label.setText(f"跟踪目标: {active_tracks}")

        # 清空并重新填充跟踪表格
        self.tracking_table.clearContents()
        self.tracking_table.setRowCount(len(tracks_info))

        if tracks_info:
            for i, (track_id, info) in enumerate(tracks_info.items()):
                # 计算轨迹长度
                trail_length = info.get('trail_length', 0)
                last_seen = info.get('last_seen', time.time())

                # 格式化最后更新时间
                time_diff = time.time() - last_seen
                if time_diff < 1:
                    last_seen_str = "刚刚"
                elif time_diff < 60:
                    last_seen_str = f"{int(time_diff)}秒前"
                else:
                    last_seen_str = f"{int(time_diff/60)}分钟前"

                # 获取类别名称
                class_name = info.get('class_name', f"Class_{info.get('class', 0)}")

                # 填充表格
                self.tracking_table.setItem(i, 0, QTableWidgetItem(str(track_id)))
                self.tracking_table.setItem(i, 1, QTableWidgetItem(class_name))
                self.tracking_table.setItem(i, 2, QTableWidgetItem(f"{info.get('confidence', 0.0):.3f}"))
                self.tracking_table.setItem(i, 3, QTableWidgetItem(f"{info.get('center', [0, 0])[0]:.0f}"))
                self.tracking_table.setItem(i, 4, QTableWidgetItem(f"{info.get('center', [0, 0])[1]:.0f}"))
                self.tracking_table.setItem(i, 5, QTableWidgetItem(str(trail_length)))
                self.tracking_table.setItem(i, 6, QTableWidgetItem(last_seen_str))

        # 强制刷新表格显示
        self.tracking_table.viewport().update()
    
    def add_log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        color_map = {
            "INFO": "#ffffff",
            "WARNING": "#ffaa00", 
            "ERROR": "#ff4444",
            "SUCCESS": "#00ff00"
        }
        color = color_map.get(level, "#ffffff")
        
        formatted_message = f'<span style="color: #888;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def update_display(self):
        """更新显示"""
        # 更新性能监控数据
        self.update_performance_monitoring()

    def update_performance_monitoring(self):
        """更新性能监控"""
        try:
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_progress.setValue(int(cpu_percent))
            self.cpu_label.setText(f"{cpu_percent:.1f}%")

            # 获取内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_progress.setValue(int(memory_percent))
            self.memory_label.setText(f"{memory_percent:.1f}%")

            # 获取GPU使用率（如果可用）
            if torch.cuda.is_available():
                try:
                    # 获取GPU内存使用情况
                    gpu_memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                    gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
                    gpu_percent = (gpu_memory_allocated / gpu_memory_total) * 100

                    self.gpu_progress.setValue(int(gpu_percent))
                    self.gpu_label.setText(f"{gpu_percent:.1f}% ({gpu_memory_allocated:.1f}GB)")
                except:
                    self.gpu_progress.setValue(0)
                    self.gpu_label.setText("N/A")
            else:
                self.gpu_progress.setValue(0)
                self.gpu_label.setText("N/A")

            # 添加到性能历史
            current_time = time.strftime("%H:%M:%S")
            performance_entry = f"[{current_time}] CPU: {cpu_percent:.1f}% | 内存: {memory_percent:.1f}%"

            if torch.cuda.is_available():
                try:
                    gpu_memory_allocated = torch.cuda.memory_allocated() / 1024**3
                    performance_entry += f" | GPU: {gpu_memory_allocated:.1f}GB"
                except:
                    pass

            self.performance_history.append(performance_entry)

            # 限制历史记录长度
            if len(self.performance_history) > 50:
                self.performance_history.pop(0)

            # 更新性能历史显示
            self.performance_text.clear()
            self.performance_text.append("\n".join(self.performance_history[-10:]))  # 显示最近10条

            # 自动滚动到底部
            scrollbar = self.performance_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            # 如果性能监控失败，显示错误信息
            self.cpu_label.setText("错误")
            self.memory_label.setText("错误")
            self.gpu_label.setText("错误")
    
    def clear_results(self):
        """清空结果"""
        self.detection_table.setRowCount(0)
        self.tracking_table.setRowCount(0)
        self.log_text.clear()
        self.detection_count_label.setText("检测目标: 0")
        self.tracking_count_label.setText("跟踪目标: 0")
        self.active_tracks_label.setText("活跃轨迹: 0")
        self.total_tracks_label.setText("总轨迹数: 0")
        self.avg_trail_length_label.setText("平均轨迹长度: 0")
    
    def export_results(self):
        """导出结果"""
        self.export_requested.emit("results")
