# YOLO全能检测系统

基于PySide6和Ultralytics YOLO开发的全功能目标检测和跟踪系统，支持所有YOLO版本和检测任务。

## 功能特性

### 🎯 核心功能
- **支持所有YOLO版本**: YOLOv5, YOLOv8, YOLOv9, YOLOv10, YOLO11等
- **多种检测任务**: 目标检测、实例分割、姿态估计、图像分类、有向边界框检测
- **目标跟踪**: 支持BoT-SORT和ByteTrack跟踪算法
- **实时处理**: 支持摄像头、视频文件、图像文件、RTSP流等多种输入源
- **高度自定义**: 所有参数可通过GUI界面调整

### 🖥️ 用户界面
- **现代化GUI**: 基于PySide6开发，支持暗色主题
- **实时预览**: 实时显示检测和跟踪结果
- **参数控制**: 直观的参数调整界面
- **结果展示**: 详细的检测和跟踪结果显示
- **性能监控**: 实时FPS和性能统计

### 📊 跟踪功能
- **多目标跟踪**: 同时跟踪多个目标
- **轨迹可视化**: 显示目标运动轨迹
- **跟踪统计**: 提供详细的跟踪统计信息
- **数据导出**: 支持跟踪数据导出

## 安装说明

### 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 安装依赖
```bash
pip install -r requirements.txt
```

### 手动安装依赖
```bash
pip install ultralytics>=8.0.0
pip install PySide6>=6.5.0
pip install opencv-python>=4.8.0
pip install numpy>=1.24.0
pip install Pillow>=9.5.0
pip install PyYAML>=6.0
pip install torch>=2.0.0
pip install torchvision>=0.15.0
pip install matplotlib>=3.7.0
pip install seaborn>=0.12.0
pip install pandas>=2.0.0
pip install tqdm>=4.65.0
```

## 使用方法

### 启动程序
```bash
python main.py
```

### 基本使用流程

1. **选择模型**
   - 在"模型配置"标签页选择YOLO模型
   - 支持官方预训练模型和自定义模型
   - 自动检测模型类型（检测/分割/姿态/分类/OBB）

2. **配置参数**
   - 在"检测参数"标签页调整置信度、IoU阈值等
   - 在"跟踪参数"标签页启用和配置目标跟踪
   - 所有参数支持实时调整

3. **选择输入源**
   - 摄像头：选择摄像头设备
   - 视频文件：选择本地视频文件
   - 图像文件：选择单张图像
   - RTSP流：输入网络摄像头地址

4. **开始检测**
   - 点击工具栏"开始检测"按钮
   - 实时查看检测结果
   - 在结果面板查看详细信息

### 高级功能

#### 目标跟踪
1. 在"跟踪参数"标签页启用"启用目标跟踪"
2. 选择跟踪算法（BoT-SORT或ByteTrack）
3. 调整轨迹历史长度等参数
4. 开始检测后可看到目标轨迹

#### 配置管理
- 支持保存和加载配置文件
- 支持导入/导出配置
- 自动保存当前配置

#### 结果导出
- 支持导出跟踪数据为JSON格式
- 支持截图保存当前帧
- 支持导出检测结果

## 项目结构

```
yolo-track-detection/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── README.md                 # 说明文档
├── ui/                       # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py        # 主窗口
│   └── widgets/              # UI组件
│       ├── video_widget.py   # 视频显示组件
│       ├── control_panel.py  # 控制面板
│       └── result_panel.py   # 结果显示面板
├── core/                     # 核心功能模块
│   ├── __init__.py
│   ├── detector.py           # 检测器核心
│   ├── tracker.py            # 跟踪器
│   ├── config_manager.py     # 配置管理
│   └── utils.py              # 工具函数
└── configs/                  # 配置文件
    ├── default.yaml          # 默认配置
    └── trackers/             # 跟踪器配置
        ├── botsort.yaml
        └── bytetrack.yaml
```

## 支持的模型

### YOLO检测模型
- YOLOv5: yolov5n.pt, yolov5s.pt, yolov5m.pt, yolov5l.pt, yolov5x.pt
- YOLOv8: yolov8n.pt, yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt
- YOLO11: yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt

### 分割模型
- YOLO11-seg: yolo11n-seg.pt, yolo11s-seg.pt, yolo11m-seg.pt, yolo11l-seg.pt, yolo11x-seg.pt

### 姿态估计模型
- YOLO11-pose: yolo11n-pose.pt, yolo11s-pose.pt, yolo11m-pose.pt, yolo11l-pose.pt, yolo11x-pose.pt

### 分类模型
- YOLO11-cls: yolo11n-cls.pt, yolo11s-cls.pt, yolo11m-cls.pt, yolo11l-cls.pt, yolo11x-cls.pt

### OBB检测模型
- YOLO11-obb: yolo11n-obb.pt, yolo11s-obb.pt, yolo11m-obb.pt, yolo11l-obb.pt, yolo11x-obb.pt

## 跟踪算法

### BoT-SORT
- 基于运动和外观的多目标跟踪
- 支持重新识别(ReID)
- 适合复杂场景和遮挡情况

### ByteTrack
- 基于字节跟踪的简单高效算法
- 低延迟，高性能
- 适合实时应用

## 配置说明

### 检测参数
- **置信度阈值**: 检测结果的最低置信度
- **IoU阈值**: 非极大值抑制的IoU阈值
- **最大检测数**: 每帧最大检测目标数量
- **图像尺寸**: 输入图像的处理尺寸

### 跟踪参数
- **跟踪器类型**: BoT-SORT或ByteTrack
- **轨迹历史长度**: 保存的轨迹点数量
- **持续跟踪**: 是否在帧间保持跟踪状态

## 常见问题

### Q: 如何添加自定义模型？
A: 在模型配置页面点击"浏览"按钮选择自定义的.pt模型文件。

### Q: 为什么检测速度很慢？
A: 可以尝试：
1. 降低图像尺寸
2. 使用更小的模型（如yolo11n.pt）
3. 启用GPU加速
4. 关闭不必要的可视化选项

### Q: 跟踪效果不好怎么办？
A: 可以调整：
1. 提高置信度阈值
2. 调整跟踪器参数
3. 增加轨迹历史长度
4. 尝试不同的跟踪算法

### Q: 如何使用RTSP摄像头？
A: 在输入源页面选择"RTSP流"，输入格式如：
```
rtsp://username:password@ip:port/stream
```

## 技术支持

如有问题或建议，请提交Issue或联系开发团队。

## 许可证

本项目基于MIT许可证开源。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持所有YOLO模型和检测任务
- 实现目标跟踪功能
- 完整的GUI界面
- 配置管理系统
