"""
工具函数模块
提供各种辅助功能
"""

import os
import cv2
import numpy as np
from typing import List, Tuple, Optional, Union
from pathlib import Path
import time
from collections import defaultdict


def is_image_file(file_path: str) -> bool:
    """
    检查文件是否为图像文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否为图像文件
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    return Path(file_path).suffix.lower() in image_extensions


def is_video_file(file_path: str) -> bool:
    """
    检查文件是否为视频文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否为视频文件
    """
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp'}
    return Path(file_path).suffix.lower() in video_extensions


def get_video_info(video_path: str) -> dict:
    """
    获取视频信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        视频信息字典
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return {}
    
    info = {
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS)
    }
    
    cap.release()
    return info


def resize_image(image: np.ndarray, target_size: Tuple[int, int], 
                keep_aspect_ratio: bool = True) -> np.ndarray:
    """
    调整图像大小
    
    Args:
        image: 输入图像
        target_size: 目标尺寸 (width, height)
        keep_aspect_ratio: 是否保持宽高比
        
    Returns:
        调整后的图像
    """
    if not keep_aspect_ratio:
        return cv2.resize(image, target_size)
    
    h, w = image.shape[:2]
    target_w, target_h = target_size
    
    # 计算缩放比例
    scale = min(target_w / w, target_h / h)
    new_w, new_h = int(w * scale), int(h * scale)
    
    # 调整大小
    resized = cv2.resize(image, (new_w, new_h))
    
    # 创建目标尺寸的画布
    canvas = np.zeros((target_h, target_w, 3), dtype=np.uint8)
    
    # 计算居中位置
    y_offset = (target_h - new_h) // 2
    x_offset = (target_w - new_w) // 2
    
    # 将调整后的图像放置在画布中心
    canvas[y_offset:y_offset + new_h, x_offset:x_offset + new_w] = resized
    
    return canvas


def draw_tracking_trails(image: np.ndarray, track_history: dict, 
                        trail_length: int = 30) -> np.ndarray:
    """
    绘制跟踪轨迹
    
    Args:
        image: 输入图像
        track_history: 跟踪历史字典
        trail_length: 轨迹长度
        
    Returns:
        绘制轨迹后的图像
    """
    for track_id, points in track_history.items():
        if len(points) > 1:
            # 将点转换为numpy数组
            pts = np.array(points, dtype=np.int32).reshape((-1, 1, 2))
            
            # 绘制轨迹线
            cv2.polylines(image, [pts], isClosed=False, 
                         color=(230, 230, 230), thickness=2)
            
            # 绘制最新点
            if len(points) > 0:
                cv2.circle(image, tuple(points[-1]), 3, (0, 255, 0), -1)
    
    return image


def calculate_fps(frame_times: List[float], window_size: int = 30) -> float:
    """
    计算FPS
    
    Args:
        frame_times: 帧时间列表
        window_size: 计算窗口大小
        
    Returns:
        FPS值
    """
    if len(frame_times) < 2:
        return 0.0
    
    # 使用最近的帧时间计算FPS
    recent_times = frame_times[-window_size:]
    if len(recent_times) < 2:
        return 0.0
    
    time_diff = recent_times[-1] - recent_times[0]
    if time_diff > 0:
        return (len(recent_times) - 1) / time_diff
    
    return 0.0


def format_time(seconds: float) -> str:
    """
    格式化时间显示
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"


def create_output_directory(base_dir: str = "runs/detect") -> str:
    """
    创建输出目录
    
    Args:
        base_dir: 基础目录
        
    Returns:
        创建的目录路径
    """
    base_path = Path(base_dir)
    
    # 如果基础目录不存在，创建第一个目录
    if not base_path.exists():
        output_dir = base_path / "exp"
        output_dir.mkdir(parents=True, exist_ok=True)
        return str(output_dir)
    
    # 查找下一个可用的exp目录
    exp_dirs = [d for d in base_path.iterdir() 
                if d.is_dir() and d.name.startswith('exp')]
    
    if not exp_dirs:
        exp_num = 1
    else:
        # 提取数字并找到最大值
        numbers = []
        for d in exp_dirs:
            name = d.name
            if name == 'exp':
                numbers.append(1)
            elif name.startswith('exp') and name[3:].isdigit():
                numbers.append(int(name[3:]))
        
        exp_num = max(numbers) + 1 if numbers else 1
    
    if exp_num == 1:
        output_dir = base_path / "exp"
    else:
        output_dir = base_path / f"exp{exp_num}"
    
    output_dir.mkdir(parents=True, exist_ok=True)
    return str(output_dir)


def get_available_cameras() -> List[int]:
    """
    获取可用的摄像头设备
    
    Returns:
        可用摄像头ID列表
    """
    available_cameras = []
    
    # 测试前10个摄像头设备
    for i in range(10):
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, _ = cap.read()
            if ret:
                available_cameras.append(i)
        cap.release()
    
    return available_cameras


def validate_rtsp_url(url: str) -> bool:
    """
    验证RTSP URL是否有效
    
    Args:
        url: RTSP URL
        
    Returns:
        是否有效
    """
    if not url.startswith(('rtsp://', 'rtmp://', 'http://', 'https://')):
        return False
    
    try:
        cap = cv2.VideoCapture(url)
        if cap.isOpened():
            ret, _ = cap.read()
            cap.release()
            return ret
    except:
        pass
    
    return False


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.frame_times = []
        self.processing_times = []
        self.start_time = None
    
    def start_frame(self):
        """开始帧处理计时"""
        self.start_time = time.time()
    
    def end_frame(self):
        """结束帧处理计时"""
        if self.start_time is not None:
            processing_time = time.time() - self.start_time
            self.processing_times.append(processing_time)
            self.frame_times.append(time.time())
            
            # 保持最近100帧的记录
            if len(self.processing_times) > 100:
                self.processing_times.pop(0)
                self.frame_times.pop(0)
    
    def get_fps(self) -> float:
        """获取FPS"""
        return calculate_fps(self.frame_times)
    
    def get_avg_processing_time(self) -> float:
        """获取平均处理时间"""
        if not self.processing_times:
            return 0.0
        return sum(self.processing_times) / len(self.processing_times)
    
    def get_stats(self) -> dict:
        """获取性能统计信息"""
        return {
            'fps': self.get_fps(),
            'avg_processing_time': self.get_avg_processing_time(),
            'frame_count': len(self.frame_times)
        }
