"""
跟踪器模块
管理目标跟踪功能
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
import time

from .config_manager import ConfigManager


class TrackingManager:
    """跟踪管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化跟踪管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.track_history = defaultdict(list)
        self.track_colors = {}
        self.track_info = {}
        self.frame_count = 0
        
        # 跟踪参数
        self.max_history_length = config_manager.get('tracking.track_history', 30)
        self.trail_thickness = 2
        self.point_radius = 3
        
        # 颜色生成器
        self.color_palette = self._generate_color_palette()
    
    def _generate_color_palette(self, num_colors: int = 100) -> List[Tuple[int, int, int]]:
        """
        生成颜色调色板
        
        Args:
            num_colors: 颜色数量
            
        Returns:
            颜色列表
        """
        colors = []
        for i in range(num_colors):
            hue = int(180 * i / num_colors)
            color = cv2.cvtColor(np.uint8([[[hue, 255, 255]]]), cv2.COLOR_HSV2BGR)[0][0]
            colors.append((int(color[0]), int(color[1]), int(color[2])))
        return colors
    
    def get_track_color(self, track_id: int) -> Tuple[int, int, int]:
        """
        获取跟踪ID对应的颜色
        
        Args:
            track_id: 跟踪ID
            
        Returns:
            BGR颜色元组
        """
        if track_id not in self.track_colors:
            color_index = len(self.track_colors) % len(self.color_palette)
            self.track_colors[track_id] = self.color_palette[color_index]
        
        return self.track_colors[track_id]
    
    def update_tracks(self, results) -> None:
        """
        更新跟踪信息

        Args:
            results: YOLO跟踪结果
        """
        self.frame_count += 1
        current_time = time.time()

        if results and len(results) > 0:
            result = results[0]

            # 检查是否有跟踪信息
            if hasattr(result, 'boxes') and result.boxes is not None:
                boxes = result.boxes

                # 检查是否有跟踪ID
                if hasattr(boxes, 'id') and boxes.id is not None:
                    track_ids = boxes.id.cpu().numpy().astype(int)
                    xyxy = boxes.xyxy.cpu().numpy()
                    conf = boxes.conf.cpu().numpy()
                    cls = boxes.cls.cpu().numpy().astype(int)

                    # 获取类别名称映射
                    names = getattr(result, 'names', {})

                    # 更新每个跟踪目标
                    for i, track_id in enumerate(track_ids):
                        # 计算中心点
                        x1, y1, x2, y2 = xyxy[i]
                        center_x = int((x1 + x2) / 2)
                        center_y = int((y1 + y2) / 2)

                        # 更新跟踪历史
                        self.track_history[track_id].append((center_x, center_y))

                        # 限制历史长度
                        if len(self.track_history[track_id]) > self.max_history_length:
                            self.track_history[track_id].pop(0)

                        # 获取类别名称
                        class_id = int(cls[i])
                        class_name = names.get(class_id, f"Class_{class_id}")

                        # 更新跟踪信息
                        self.track_info[track_id] = {
                            'bbox': (x1, y1, x2, y2),
                            'confidence': float(conf[i]),
                            'class': class_id,
                            'class_name': class_name,
                            'center': (center_x, center_y),
                            'last_seen': current_time,
                            'frame_count': self.frame_count,
                            'trail_length': len(self.track_history[track_id])
                        }
        
        # 清理长时间未见的跟踪目标
        self._cleanup_old_tracks(current_time)
    
    def _cleanup_old_tracks(self, current_time: float, timeout: float = 5.0):
        """
        清理长时间未见的跟踪目标
        
        Args:
            current_time: 当前时间
            timeout: 超时时间（秒）
        """
        tracks_to_remove = []
        
        for track_id, info in self.track_info.items():
            if current_time - info['last_seen'] > timeout:
                tracks_to_remove.append(track_id)
        
        for track_id in tracks_to_remove:
            if track_id in self.track_history:
                del self.track_history[track_id]
            if track_id in self.track_info:
                del self.track_info[track_id]
            if track_id in self.track_colors:
                del self.track_colors[track_id]
    
    def draw_tracks(self, image: np.ndarray, show_trails: bool = True, 
                   show_ids: bool = True, show_info: bool = True) -> np.ndarray:
        """
        在图像上绘制跟踪信息
        
        Args:
            image: 输入图像
            show_trails: 是否显示轨迹
            show_ids: 是否显示ID
            show_info: 是否显示详细信息
            
        Returns:
            绘制后的图像
        """
        if not self.track_history:
            return image
        
        # 绘制轨迹
        if show_trails:
            for track_id, points in self.track_history.items():
                if len(points) > 1:
                    color = self.get_track_color(track_id)
                    
                    # 绘制轨迹线
                    pts = np.array(points, dtype=np.int32).reshape((-1, 1, 2))
                    cv2.polylines(image, [pts], isClosed=False, 
                                color=color, thickness=self.trail_thickness)
                    
                    # 绘制最新点
                    if len(points) > 0:
                        cv2.circle(image, tuple(points[-1]), self.point_radius, 
                                 color, -1)
        
        # 绘制ID和信息
        if show_ids or show_info:
            for track_id, info in self.track_info.items():
                color = self.get_track_color(track_id)
                center_x, center_y = info['center']
                
                if show_ids:
                    # 绘制跟踪ID
                    cv2.putText(image, f"ID:{track_id}", 
                              (center_x - 20, center_y - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                if show_info:
                    # 绘制置信度
                    conf_text = f"{info['confidence']:.2f}"
                    cv2.putText(image, conf_text,
                              (center_x - 20, center_y + 15),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return image
    
    def get_track_statistics(self) -> Dict[str, Any]:
        """
        获取跟踪统计信息
        
        Returns:
            跟踪统计字典
        """
        active_tracks = len(self.track_info)
        total_tracks = len(self.track_colors)
        
        # 计算平均轨迹长度
        avg_trail_length = 0
        if self.track_history:
            total_length = sum(len(points) for points in self.track_history.values())
            avg_trail_length = total_length / len(self.track_history)
        
        # 获取最长轨迹
        max_trail_length = 0
        longest_track_id = None
        for track_id, points in self.track_history.items():
            if len(points) > max_trail_length:
                max_trail_length = len(points)
                longest_track_id = track_id
        
        return {
            'active_tracks': active_tracks,
            'total_tracks_seen': total_tracks,
            'avg_trail_length': avg_trail_length,
            'max_trail_length': max_trail_length,
            'longest_track_id': longest_track_id,
            'frame_count': self.frame_count
        }
    
    def get_track_info(self, track_id: int) -> Optional[Dict[str, Any]]:
        """
        获取指定跟踪ID的信息
        
        Args:
            track_id: 跟踪ID
            
        Returns:
            跟踪信息字典
        """
        return self.track_info.get(track_id)
    
    def get_all_tracks_info(self) -> Dict[int, Dict[str, Any]]:
        """
        获取所有跟踪目标的信息
        
        Returns:
            所有跟踪信息字典
        """
        return self.track_info.copy()
    
    def clear_tracks(self):
        """清除所有跟踪信息"""
        self.track_history.clear()
        self.track_colors.clear()
        self.track_info.clear()
        self.frame_count = 0
    
    def export_tracks(self, file_path: str) -> bool:
        """
        导出跟踪数据
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            import json
            
            export_data = {
                'track_history': {str(k): v for k, v in self.track_history.items()},
                'track_info': {str(k): v for k, v in self.track_info.items()},
                'frame_count': self.frame_count,
                'statistics': self.get_track_statistics()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            return True
        except Exception as e:
            print(f"导出跟踪数据失败: {e}")
            return False
    
    def set_trail_style(self, thickness: int = 2, point_radius: int = 3):
        """
        设置轨迹样式
        
        Args:
            thickness: 线条粗细
            point_radius: 点半径
        """
        self.trail_thickness = thickness
        self.point_radius = point_radius
    
    def set_history_length(self, length: int):
        """
        设置历史长度
        
        Args:
            length: 历史长度
        """
        self.max_history_length = length
        self.config_manager.set('tracking.track_history', length)
        
        # 截断现有历史
        for track_id in self.track_history:
            if len(self.track_history[track_id]) > length:
                self.track_history[track_id] = self.track_history[track_id][-length:]
