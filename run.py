#!/usr/bin/env python3
"""
YOLO检测系统启动脚本
简化版启动入口，用于快速测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    try:
        print("正在启动YOLO全能检测系统...")
        print("=" * 50)
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return 1
        
        print(f"Python版本: {sys.version}")
        
        # 检查关键依赖
        try:
            import PySide6
            print(f"PySide6版本: {PySide6.__version__}")
        except ImportError:
            print("错误: 未安装PySide6，请运行: pip install PySide6")
            return 1
        
        try:
            import ultralytics
            print(f"Ultralytics版本: {ultralytics.__version__}")
        except ImportError:
            print("错误: 未安装ultralytics，请运行: pip install ultralytics")
            return 1
        
        try:
            import cv2
            print(f"OpenCV版本: {cv2.__version__}")
        except ImportError:
            print("错误: 未安装opencv-python，请运行: pip install opencv-python")
            return 1
        
        print("=" * 50)
        print("依赖检查完成，正在启动主程序...")
        
        # 导入并运行主程序
        from main import main as main_app
        return main_app()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
