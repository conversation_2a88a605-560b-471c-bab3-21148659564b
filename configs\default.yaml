detection:
  augment: false
  conf: 0.25
  device: '1'
  half: false
  imgsz: 640
  iou: 0.45
  line_width: 3
  max_det: 1000
  save: false
  save_conf: false
  save_crop: false
  save_txt: false
  show_boxes: true
  show_conf: true
  show_labels: true
  visualize: false
model:
  default_model: yolov5n.pt
  supported_models:
  - yolov5n.pt
  - yolov5s.pt
  - yolov5m.pt
  - yolov5l.pt
  - yolov5x.pt
  - yolov8n.pt
  - yolov8s.pt
  - yolov8m.pt
  - yolov8l.pt
  - yolov8x.pt
  - yolo11n.pt
  - yolo11s.pt
  - yolo11m.pt
  - yolo11l.pt
  - yolo11x.pt
  - yolo11n-seg.pt
  - yolo11s-seg.pt
  - yolo11m-seg.pt
  - yolo11l-seg.pt
  - yolo11x-seg.pt
  - yolo11n-pose.pt
  - yolo11s-pose.pt
  - yolo11m-pose.pt
  - yolo11l-pose.pt
  - yolo11x-pose.pt
  - yolo11n-cls.pt
  - yolo11s-cls.pt
  - yolo11m-cls.pt
  - yolo11l-cls.pt
  - yolo11x-cls.pt
  - yolo11n-obb.pt
  - yolo11s-obb.pt
  - yolo11m-obb.pt
  - yolo11l-obb.pt
  - yolo11x-obb.pt
output:
  format: mp4
  fourcc: mp4v
  fps: 30
  quality: 95
  save_dir: runs/detect
performance:
  cache_size: 100
  gpu_memory_fraction: 0.8
  max_threads: 4
  multi_threading: true
source:
  batch_dir: ''
  camera_id: 0
  image_path: C:/Users/<USER>/Desktop/yolo-track-detection/bus.jpg
  rtsp_url: ''
  type: video
  video_path: C:/Users/<USER>/Downloads/4b9e9e8962d54b8e5208c6438265da88.mp4
tracking:
  enabled: true
  persist: true
  track_history: 30
  tracker: botsort.yaml
ui:
  auto_save: true
  language: zh_CN
  theme: dark
  title: YOLO全能检测系统
  window_size:
  - 1400
  - 900
