# YOLO检测系统默认配置

# 模型配置
model:
  # 默认模型路径
  default_model: "yolo11n.pt"
  # 支持的模型类型
  supported_models:
    - "yolov5n.pt"
    - "yolov5s.pt"
    - "yolov5m.pt"
    - "yolov5l.pt"
    - "yolov5x.pt"
    - "yolov8n.pt"
    - "yolov8s.pt"
    - "yolov8m.pt"
    - "yolov8l.pt"
    - "yolov8x.pt"
    - "yolo11n.pt"
    - "yolo11s.pt"
    - "yolo11m.pt"
    - "yolo11l.pt"
    - "yolo11x.pt"
    - "yolo11n-seg.pt"
    - "yolo11s-seg.pt"
    - "yolo11m-seg.pt"
    - "yolo11l-seg.pt"
    - "yolo11x-seg.pt"
    - "yolo11n-pose.pt"
    - "yolo11s-pose.pt"
    - "yolo11m-pose.pt"
    - "yolo11l-pose.pt"
    - "yolo11x-pose.pt"
    - "yolo11n-cls.pt"
    - "yolo11s-cls.pt"
    - "yolo11m-cls.pt"
    - "yolo11l-cls.pt"
    - "yolo11x-cls.pt"
    - "yolo11n-obb.pt"
    - "yolo11s-obb.pt"
    - "yolo11m-obb.pt"
    - "yolo11l-obb.pt"
    - "yolo11x-obb.pt"

# 检测参数
detection:
  # 置信度阈值
  conf: 0.25
  # IoU阈值
  iou: 0.45
  # 最大检测数量
  max_det: 1000
  # 图像尺寸
  imgsz: 640
  # 设备 (cpu, 0, 1, 2, ...)
  device: "auto"
  # 半精度推理
  half: false
  # 数据增强
  augment: false
  # 可视化
  visualize: false
  # 保存结果
  save: false
  # 保存置信度
  save_conf: false
  # 保存文本结果
  save_txt: false
  # 保存裁剪图像
  save_crop: false
  # 显示标签
  show_labels: true
  # 显示置信度
  show_conf: true
  # 显示边界框
  show_boxes: true
  # 线条粗细
  line_width: 3

# 跟踪参数
tracking:
  # 启用跟踪
  enabled: false
  # 跟踪器类型 (botsort, bytetrack)
  tracker: "botsort.yaml"
  # 持续跟踪
  persist: true
  # 跟踪历史长度
  track_history: 30

# 输入源配置
source:
  # 默认输入类型 (image, video, camera, rtsp)
  type: "camera"
  # 摄像头设备ID
  camera_id: 0
  # 视频文件路径
  video_path: ""
  # 图像文件路径
  image_path: ""
  # RTSP流地址
  rtsp_url: ""
  # 批处理目录
  batch_dir: ""

# 输出配置
output:
  # 输出目录
  save_dir: "runs/detect"
  # 输出格式
  format: "mp4"
  # 视频编码器
  fourcc: "mp4v"
  # 帧率
  fps: 30
  # 视频质量
  quality: 95

# UI配置
ui:
  # 窗口标题
  title: "YOLO全能检测系统"
  # 窗口大小
  window_size: [1400, 900]
  # 主题
  theme: "dark"
  # 语言
  language: "zh_CN"
  # 自动保存配置
  auto_save: true

# 性能配置
performance:
  # 多线程处理
  multi_threading: true
  # 最大线程数
  max_threads: 4
  # 缓存大小
  cache_size: 100
  # GPU内存优化
  gpu_memory_fraction: 0.8
