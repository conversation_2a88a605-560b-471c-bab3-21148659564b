#!/usr/bin/env python3
"""
YOLO全能检测系统主程序
基于PySide6和Ultralytics YOLO开发
支持所有YOLO版本和检测任务，支持目标跟踪
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont

from ui.main_window import MainWindow


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'ultralytics',
        'opencv-python', 
        'numpy',
        'torch',
        'torchvision',
        'Pillow',
        'PyYAML'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'ultralytics':
                import ultralytics
            elif package == 'numpy':
                import numpy
            elif package == 'torch':
                import torch
            elif package == 'torchvision':
                import torchvision
            elif package == 'Pillow':
                import PIL
            elif package == 'PyYAML':
                import yaml
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages


def show_splash_screen(app):
    """显示启动画面"""
    # 创建启动画面
    splash_pix = QPixmap(500, 350)

    # 创建渐变背景
    from PySide6.QtGui import QPainter, QLinearGradient, QColor, QPen

    painter = QPainter(splash_pix)
    painter.setRenderHint(QPainter.Antialiasing)

    # 创建渐变
    gradient = QLinearGradient(0, 0, 500, 350)
    gradient.setColorAt(0, QColor(26, 26, 26))
    gradient.setColorAt(0.5, QColor(74, 144, 226))
    gradient.setColorAt(1, QColor(45, 45, 45))

    painter.fillRect(splash_pix.rect(), gradient)

    # 绘制边框
    pen = QPen(QColor(74, 144, 226), 3)
    painter.setPen(pen)
    painter.drawRoundedRect(splash_pix.rect().adjusted(2, 2, -2, -2), 15, 15)

    # 绘制标题
    title_font = QFont("Arial", 24, QFont.Bold)
    painter.setFont(title_font)
    painter.setPen(QColor(255, 255, 255))
    painter.drawText(splash_pix.rect().adjusted(0, 50, 0, -150), Qt.AlignCenter, "YOLO全能检测系统")

    # 绘制副标题
    subtitle_font = QFont("Arial", 12)
    painter.setFont(subtitle_font)
    painter.setPen(QColor(200, 200, 200))
    painter.drawText(splash_pix.rect().adjusted(0, 100, 0, -100), Qt.AlignCenter, "基于PySide6和Ultralytics YOLO")

    # 绘制版本信息
    version_font = QFont("Arial", 10)
    painter.setFont(version_font)
    painter.setPen(QColor(150, 150, 150))
    painter.drawText(splash_pix.rect().adjusted(0, 150, 0, -50), Qt.AlignCenter, "版本 1.0.0")

    painter.end()

    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)

    # 设置字体
    font = QFont("Arial", 11, QFont.Bold)
    splash.setFont(font)

    # 显示启动画面
    splash.show()
    splash.showMessage("正在初始化系统...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)

    # 处理事件
    app.processEvents()

    return splash


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("YOLO全能检测系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("YOLO Detection Team")
    app.setOrganizationDomain("yolo-detection.com")
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    # 设置现代化暗色主题
    dark_palette = """
    QMainWindow {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 #1a1a1a, stop:1 #2d2d2d);
        color: #ffffff;
    }
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
        font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    }
    QGroupBox {
        font-weight: bold;
        border: 2px solid #4a90e2;
        border-radius: 8px;
        margin-top: 1ex;
        padding-top: 15px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3a3a3a, stop:1 #2a2a2a);
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 8px 0 8px;
        color: #4a90e2;
        font-size: 12px;
        font-weight: bold;
    }
    QPushButton {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4a90e2, stop:1 #357abd);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 11px;
        min-height: 20px;
    }
    QPushButton:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4080cd);
        transform: translateY(-1px);
    }
    QPushButton:pressed {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #357abd, stop:1 #2a5f8f);
    }
    QPushButton:disabled {
        background-color: #555;
        color: #888;
    }
    QComboBox {
        border: 2px solid #4a90e2;
        border-radius: 6px;
        padding: 8px 12px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        color: #ffffff;
        font-size: 11px;
        min-height: 20px;
    }
    QComboBox:hover {
        border: 2px solid #5ba0f2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4c4c4c, stop:1 #3c3c3c);
    }
    QComboBox::drop-down {
        border: none;
        width: 20px;
    }
    QComboBox::down-arrow {
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #4a90e2;
    }
    QLineEdit {
        border: 2px solid #4a90e2;
        border-radius: 6px;
        padding: 8px 12px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        color: #ffffff;
        font-size: 11px;
        min-height: 20px;
    }
    QLineEdit:focus {
        border: 2px solid #5ba0f2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4c4c4c, stop:1 #3c3c3c);
    }
    QSpinBox, QDoubleSpinBox {
        border: 2px solid #4a90e2;
        border-radius: 6px;
        padding: 8px 12px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        color: #ffffff;
        font-size: 11px;
        min-height: 20px;
    }
    QSpinBox:hover, QDoubleSpinBox:hover {
        border: 2px solid #5ba0f2;
    }
    QSlider::groove:horizontal {
        border: 2px solid #4a90e2;
        height: 6px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #2c2c2c, stop:1 #1c1c1c);
        border-radius: 6px;
    }
    QSlider::handle:horizontal {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
        border: 2px solid #ffffff;
        width: 20px;
        margin: -8px 0;
        border-radius: 12px;
    }
    QSlider::handle:horizontal:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #6bb0ff, stop:1 #5ba0f2);
        border: 2px solid #ffffff;
    }
    QCheckBox {
        font-size: 11px;
        color: #ffffff;
        spacing: 8px;
    }
    QCheckBox::indicator {
        width: 20px;
        height: 20px;
    }
    QCheckBox::indicator:unchecked {
        border: 2px solid #4a90e2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border-radius: 6px;
    }
    QCheckBox::indicator:unchecked:hover {
        border: 2px solid #5ba0f2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4c4c4c, stop:1 #3c3c3c);
    }
    QCheckBox::indicator:checked {
        border: 2px solid #4a90e2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
        border-radius: 6px;
    }
    QCheckBox::indicator:checked:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #6bb0ff, stop:1 #5ba0f2);
    }
    QTabWidget::pane {
        border: 2px solid #4a90e2;
        border-radius: 8px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #2b2b2b, stop:1 #1b1b1b);
        margin-top: 10px;
    }
    QTabBar::tab {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border: 2px solid #4a90e2;
        padding: 10px 20px;
        margin-right: 2px;
        border-radius: 6px;
        color: #ffffff;
        font-weight: bold;
        font-size: 11px;
    }
    QTabBar::tab:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4c4c4c, stop:1 #3c3c3c);
        border: 2px solid #5ba0f2;
    }
    QTabBar::tab:selected {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
        color: white;
        border: 2px solid #ffffff;
    }
    QTableWidget {
        gridline-color: #4a90e2;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #2b2b2b, stop:1 #1b1b1b);
        alternate-background-color: #3c3c3c;
        border: 2px solid #4a90e2;
        border-radius: 6px;
        color: #ffffff;
    }
    QHeaderView::section {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #4a90e2, stop:1 #357abd);
        border: 1px solid #2c5f8f;
        padding: 8px;
        font-weight: bold;
        color: #ffffff;
        font-size: 11px;
    }
    QMenuBar {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border-bottom: 2px solid #4a90e2;
        color: #ffffff;
    }
    QMenuBar::item {
        padding: 8px 15px;
        border-radius: 4px;
    }
    QMenuBar::item:selected {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
    }
    QMenu {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border: 2px solid #4a90e2;
        border-radius: 6px;
        color: #ffffff;
    }
    QMenu::item {
        padding: 8px 25px;
        border-radius: 4px;
    }
    QMenu::item:selected {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
    }
    QToolBar {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border: 2px solid #4a90e2;
        border-radius: 6px;
        spacing: 5px;
        padding: 5px;
    }
    QStatusBar {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        border-top: 2px solid #4a90e2;
        color: #ffffff;
        font-size: 11px;
    }
    QProgressBar {
        border: 2px solid #4a90e2;
        border-radius: 6px;
        text-align: center;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3c3c3c, stop:1 #2c2c2c);
        color: #ffffff;
        font-weight: bold;
    }
    QProgressBar::chunk {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #5ba0f2, stop:1 #4a90e2);
        border-radius: 4px;
    }
    QTextEdit {
        border: 2px solid #4a90e2;
        border-radius: 6px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #1e1e1e, stop:1 #0e0e0e);
        color: #ffffff;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 10px;
        padding: 5px;
    }
    QSplitter::handle {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #4a90e2, stop:1 #357abd);
        border-radius: 2px;
    }
    QSplitter::handle:hover {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #5ba0f2, stop:1 #4a90e2);
    }
    """
    
    app.setStyleSheet(dark_palette)
    
    return app


def main():
    """主函数"""
    try:
        # 检查依赖包
        missing_packages = check_dependencies()
        if missing_packages:
            print("缺少以下依赖包:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\n请使用以下命令安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return 1
        
        # 创建应用程序
        app = setup_application()
        
        # 显示启动画面
        splash = show_splash_screen(app)
        
        # 更新启动画面消息
        splash.showMessage("正在初始化...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 更新启动画面消息
        splash.showMessage("正在加载界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        
        # 显示主窗口
        main_window.show()
        
        # 关闭启动画面
        def close_splash():
            splash.close()
        
        QTimer.singleShot(2000, close_splash)  # 2秒后关闭启动画面
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
