#!/usr/bin/env python3
"""
YOLO全能检测系统主程序
基于PySide6和Ultralytics YOLO开发
支持所有YOLO版本和检测任务，支持目标跟踪
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont

from ui.main_window import MainWindow


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'ultralytics',
        'opencv-python', 
        'numpy',
        'torch',
        'torchvision',
        'Pillow',
        'PyYAML'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'ultralytics':
                import ultralytics
            elif package == 'numpy':
                import numpy
            elif package == 'torch':
                import torch
            elif package == 'torchvision':
                import torchvision
            elif package == 'Pillow':
                import PIL
            elif package == 'PyYAML':
                import yaml
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages


def show_splash_screen(app):
    """显示启动画面"""
    # 创建启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.darkBlue)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 设置字体
    font = QFont("Arial", 12, QFont.Bold)
    splash.setFont(font)
    
    # 显示启动画面
    splash.show()
    splash.showMessage("YOLO全能检测系统", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
    
    # 处理事件
    app.processEvents()
    
    return splash


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("YOLO全能检测系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("YOLO Detection Team")
    app.setOrganizationDomain("yolo-detection.com")
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    # 设置暗色主题
    dark_palette = """
    QMainWindow {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
    }
    QGroupBox {
        font-weight: bold;
        border: 2px solid #555;
        border-radius: 5px;
        margin-top: 1ex;
        padding-top: 10px;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
    }
    QPushButton {
        background-color: #0a84ff;
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #0056b3;
    }
    QPushButton:pressed {
        background-color: #004085;
    }
    QPushButton:disabled {
        background-color: #555;
        color: #888;
    }
    QComboBox {
        border: 1px solid #555;
        border-radius: 3px;
        padding: 5px;
        background-color: #3c3c3c;
    }
    QComboBox::drop-down {
        border: none;
    }
    QComboBox::down-arrow {
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #ffffff;
    }
    QLineEdit {
        border: 1px solid #555;
        border-radius: 3px;
        padding: 5px;
        background-color: #3c3c3c;
    }
    QSpinBox, QDoubleSpinBox {
        border: 1px solid #555;
        border-radius: 3px;
        padding: 5px;
        background-color: #3c3c3c;
    }
    QSlider::groove:horizontal {
        border: 1px solid #555;
        height: 8px;
        background: #3c3c3c;
        border-radius: 4px;
    }
    QSlider::handle:horizontal {
        background: #0a84ff;
        border: 1px solid #0a84ff;
        width: 18px;
        margin: -2px 0;
        border-radius: 9px;
    }
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
    }
    QCheckBox::indicator:unchecked {
        border: 2px solid #555;
        background-color: #3c3c3c;
        border-radius: 3px;
    }
    QCheckBox::indicator:checked {
        border: 2px solid #0a84ff;
        background-color: #0a84ff;
        border-radius: 3px;
    }
    QTabWidget::pane {
        border: 1px solid #555;
        background-color: #2b2b2b;
    }
    QTabBar::tab {
        background-color: #3c3c3c;
        border: 1px solid #555;
        padding: 8px 16px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: #0a84ff;
        color: white;
    }
    QTableWidget {
        gridline-color: #555;
        background-color: #2b2b2b;
        alternate-background-color: #3c3c3c;
    }
    QHeaderView::section {
        background-color: #3c3c3c;
        border: 1px solid #555;
        padding: 5px;
        font-weight: bold;
    }
    QMenuBar {
        background-color: #3c3c3c;
        border-bottom: 1px solid #555;
    }
    QMenuBar::item {
        padding: 5px 10px;
    }
    QMenuBar::item:selected {
        background-color: #0a84ff;
    }
    QMenu {
        background-color: #3c3c3c;
        border: 1px solid #555;
    }
    QMenu::item {
        padding: 5px 20px;
    }
    QMenu::item:selected {
        background-color: #0a84ff;
    }
    QToolBar {
        background-color: #3c3c3c;
        border: 1px solid #555;
        spacing: 3px;
    }
    QStatusBar {
        background-color: #3c3c3c;
        border-top: 1px solid #555;
    }
    QProgressBar {
        border: 1px solid #555;
        border-radius: 3px;
        text-align: center;
        background-color: #3c3c3c;
    }
    QProgressBar::chunk {
        background-color: #0a84ff;
        border-radius: 2px;
    }
    """
    
    app.setStyleSheet(dark_palette)
    
    return app


def main():
    """主函数"""
    try:
        # 检查依赖包
        missing_packages = check_dependencies()
        if missing_packages:
            print("缺少以下依赖包:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\n请使用以下命令安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return 1
        
        # 创建应用程序
        app = setup_application()
        
        # 显示启动画面
        splash = show_splash_screen(app)
        
        # 更新启动画面消息
        splash.showMessage("正在初始化...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 更新启动画面消息
        splash.showMessage("正在加载界面...", Qt.AlignCenter | Qt.AlignBottom, Qt.white)
        app.processEvents()
        
        # 显示主窗口
        main_window.show()
        
        # 关闭启动画面
        def close_splash():
            splash.close()
        
        QTimer.singleShot(2000, close_splash)  # 2秒后关闭启动画面
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
