# BoT-SORT跟踪器配置

# 跟踪器类型
tracker_type: botsort

# 跟踪阈值
track_high_thresh: 0.5
track_low_thresh: 0.1
new_track_thresh: 0.6

# 跟踪缓冲区
track_buffer: 30

# 匹配阈值
match_thresh: 0.8

# 融合分数
fuse_score: true

# 全局运动补偿方法
# 选项: orb, sift, ecc, sparseOptFlow, None
gmc_method: sparseOptFlow

# ReID相关参数
proximity_thresh: 0.5
appearance_thresh: 0.25
with_reid: false

# ReID模型
# auto: 使用检测器的本地特征
# 或指定分类模型如: yolo11n-cls.pt
model: auto
