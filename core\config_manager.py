"""
配置管理器
负责加载、保存和管理系统配置
"""

import yaml
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_file = self.config_dir / "default.yaml"
        self.config = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
            else:
                self.config = self._get_default_config()
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config = self._get_default_config()
        
        return self.config
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            是否保存成功
        """
        try:
            # 确保配置目录存在
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 'model.default_model'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            updates: 更新的配置字典
        """
        for key, value in updates.items():
            self.set(key, value)
    
    def get_tracker_config(self, tracker_name: str) -> Dict[str, Any]:
        """
        获取跟踪器配置
        
        Args:
            tracker_name: 跟踪器名称
            
        Returns:
            跟踪器配置字典
        """
        tracker_file = self.config_dir / "trackers" / f"{tracker_name}.yaml"
        
        try:
            if tracker_file.exists():
                with open(tracker_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"加载跟踪器配置失败: {e}")
        
        return {}
    
    def save_tracker_config(self, tracker_name: str, config: Dict[str, Any]) -> bool:
        """
        保存跟踪器配置
        
        Args:
            tracker_name: 跟踪器名称
            config: 跟踪器配置
            
        Returns:
            是否保存成功
        """
        try:
            tracker_dir = self.config_dir / "trackers"
            tracker_dir.mkdir(parents=True, exist_ok=True)
            
            tracker_file = tracker_dir / f"{tracker_name}.yaml"
            with open(tracker_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            return True
        except Exception as e:
            print(f"保存跟踪器配置失败: {e}")
            return False
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            "model": {
                "default_model": "yolo11n.pt",
                "supported_models": [
                    "yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt"
                ]
            },
            "detection": {
                "conf": 0.25,
                "iou": 0.45,
                "max_det": 1000,
                "imgsz": 640,
                "device": "auto",
                "half": False
            },
            "tracking": {
                "enabled": False,
                "tracker": "botsort.yaml",
                "persist": True,
                "track_history": 30
            },
            "source": {
                "type": "camera",
                "camera_id": 0
            },
            "ui": {
                "title": "YOLO全能检测系统",
                "window_size": [1400, 900],
                "theme": "dark"
            }
        }
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = self._get_default_config()
        self.save_config()
    
    def export_config(self, file_path: str) -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 导入文件路径
            
        Returns:
            是否导入成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = yaml.safe_load(f)
                if imported_config:
                    self.config.update(imported_config)
                    self.save_config()
                    return True
        except Exception as e:
            print(f"导入配置失败: {e}")
        
        return False
