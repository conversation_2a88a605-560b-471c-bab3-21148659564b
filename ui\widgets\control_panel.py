"""
控制面板组件
提供检测和跟踪参数控制
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QLabel, QSlider, QSpinBox, QDoubleSpinBox, 
                               QComboBox, QCheckBox, QPushButton, QLineEdit,
                               QFileDialog, QMessageBox, QTabWidget,
                               QFormLayout, QGridLayout)
from PySide6.QtCore import Qt, Signal
from typing import Dict, Any, List

from core.config_manager import ConfigManager
from core.utils import get_available_cameras


class ControlPanel(QWidget):
    """控制面板"""
    
    # 信号定义
    config_changed = Signal(dict)  # 配置改变信号
    model_changed = Signal(str)    # 模型改变信号
    source_changed = Signal(dict)  # 输入源改变信号
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 模型配置标签页
        self.model_tab = self.create_model_tab()
        self.tab_widget.addTab(self.model_tab, "模型配置")
        
        # 检测参数标签页
        self.detection_tab = self.create_detection_tab()
        self.tab_widget.addTab(self.detection_tab, "检测参数")
        
        # 跟踪参数标签页
        self.tracking_tab = self.create_tracking_tab()
        self.tab_widget.addTab(self.tracking_tab, "跟踪参数")
        
        # 输入源标签页
        self.source_tab = self.create_source_tab()
        self.tab_widget.addTab(self.source_tab, "输入源")
        
        layout.addWidget(self.tab_widget)
        
        # 控制按钮
        self.create_control_buttons(layout)
    
    def create_model_tab(self) -> QWidget:
        """创建模型配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模型选择组
        model_group = QGroupBox("模型选择")
        model_layout = QFormLayout(model_group)
        
        # 模型下拉框
        self.model_combo = QComboBox()
        self.model_combo.setEditable(True)
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_layout.addRow("模型:", self.model_combo)
        
        # 浏览模型文件按钮
        browse_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("或输入自定义模型路径...")
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_model_file)
        browse_layout.addWidget(self.model_path_edit)
        browse_layout.addWidget(browse_btn)
        model_layout.addRow("自定义模型:", browse_layout)
        
        # 设备选择
        self.device_combo = QComboBox()
        self.device_combo.addItems(["auto", "cpu", "cuda", "0", "1", "2", "3"])
        self.device_combo.currentTextChanged.connect(self.on_config_changed)
        model_layout.addRow("设备:", self.device_combo)
        
        # 半精度推理
        self.half_checkbox = QCheckBox("启用半精度推理(FP16)")
        self.half_checkbox.stateChanged.connect(self.on_config_changed)
        model_layout.addRow(self.half_checkbox)
        
        layout.addWidget(model_group)
        layout.addStretch()
        
        return widget
    
    def create_detection_tab(self) -> QWidget:
        """创建检测参数标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 检测阈值组
        threshold_group = QGroupBox("检测阈值")
        threshold_layout = QFormLayout(threshold_group)
        
        # 置信度阈值
        conf_layout = QHBoxLayout()
        self.conf_slider = QSlider(Qt.Horizontal)
        self.conf_slider.setRange(0, 100)
        self.conf_slider.setValue(25)
        self.conf_slider.valueChanged.connect(self.on_slider_changed)
        self.conf_spinbox = QDoubleSpinBox()
        self.conf_spinbox.setRange(0.0, 1.0)
        self.conf_spinbox.setSingleStep(0.01)
        self.conf_spinbox.setDecimals(2)
        self.conf_spinbox.valueChanged.connect(self.on_spinbox_changed)
        conf_layout.addWidget(self.conf_slider)
        conf_layout.addWidget(self.conf_spinbox)
        threshold_layout.addRow("置信度:", conf_layout)
        
        # IoU阈值
        iou_layout = QHBoxLayout()
        self.iou_slider = QSlider(Qt.Horizontal)
        self.iou_slider.setRange(0, 100)
        self.iou_slider.setValue(45)
        self.iou_slider.valueChanged.connect(self.on_slider_changed)
        self.iou_spinbox = QDoubleSpinBox()
        self.iou_spinbox.setRange(0.0, 1.0)
        self.iou_spinbox.setSingleStep(0.01)
        self.iou_spinbox.setDecimals(2)
        self.iou_spinbox.valueChanged.connect(self.on_spinbox_changed)
        iou_layout.addWidget(self.iou_slider)
        iou_layout.addWidget(self.iou_spinbox)
        threshold_layout.addRow("IoU阈值:", iou_layout)
        
        layout.addWidget(threshold_group)
        
        # 检测参数组
        params_group = QGroupBox("检测参数")
        params_layout = QFormLayout(params_group)
        
        # 最大检测数量
        self.max_det_spinbox = QSpinBox()
        self.max_det_spinbox.setRange(1, 10000)
        self.max_det_spinbox.setValue(1000)
        self.max_det_spinbox.valueChanged.connect(self.on_config_changed)
        params_layout.addRow("最大检测数:", self.max_det_spinbox)
        
        # 图像尺寸
        self.imgsz_combo = QComboBox()
        self.imgsz_combo.addItems(["320", "416", "512", "640", "800", "1024", "1280"])
        self.imgsz_combo.setCurrentText("640")
        self.imgsz_combo.currentTextChanged.connect(self.on_config_changed)
        params_layout.addRow("图像尺寸:", self.imgsz_combo)
        
        # 数据增强
        self.augment_checkbox = QCheckBox("启用数据增强")
        self.augment_checkbox.stateChanged.connect(self.on_config_changed)
        params_layout.addRow(self.augment_checkbox)
        
        layout.addWidget(params_group)
        
        # 显示选项组
        display_group = QGroupBox("显示选项")
        display_layout = QFormLayout(display_group)
        
        self.show_labels_checkbox = QCheckBox("显示标签")
        self.show_labels_checkbox.setChecked(True)
        self.show_labels_checkbox.stateChanged.connect(self.on_config_changed)
        display_layout.addRow(self.show_labels_checkbox)
        
        self.show_conf_checkbox = QCheckBox("显示置信度")
        self.show_conf_checkbox.setChecked(True)
        self.show_conf_checkbox.stateChanged.connect(self.on_config_changed)
        display_layout.addRow(self.show_conf_checkbox)
        
        self.show_boxes_checkbox = QCheckBox("显示边界框")
        self.show_boxes_checkbox.setChecked(True)
        self.show_boxes_checkbox.stateChanged.connect(self.on_config_changed)
        display_layout.addRow(self.show_boxes_checkbox)
        
        # 线条粗细
        self.line_width_spinbox = QSpinBox()
        self.line_width_spinbox.setRange(1, 10)
        self.line_width_spinbox.setValue(3)
        self.line_width_spinbox.valueChanged.connect(self.on_config_changed)
        display_layout.addRow("线条粗细:", self.line_width_spinbox)
        
        layout.addWidget(display_group)
        layout.addStretch()
        
        return widget
    
    def create_tracking_tab(self) -> QWidget:
        """创建跟踪参数标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 跟踪开关
        self.tracking_enabled_checkbox = QCheckBox("启用目标跟踪")
        self.tracking_enabled_checkbox.stateChanged.connect(self.on_tracking_enabled_changed)
        layout.addWidget(self.tracking_enabled_checkbox)
        
        # 跟踪器选择组
        tracker_group = QGroupBox("跟踪器配置")
        tracker_layout = QFormLayout(tracker_group)
        
        # 跟踪器类型
        self.tracker_combo = QComboBox()
        self.tracker_combo.addItems(["botsort.yaml", "bytetrack.yaml"])
        self.tracker_combo.currentTextChanged.connect(self.on_config_changed)
        tracker_layout.addRow("跟踪器:", self.tracker_combo)
        
        # 轨迹历史长度
        self.track_history_spinbox = QSpinBox()
        self.track_history_spinbox.setRange(5, 100)
        self.track_history_spinbox.setValue(30)
        self.track_history_spinbox.valueChanged.connect(self.on_config_changed)
        tracker_layout.addRow("轨迹历史长度:", self.track_history_spinbox)
        
        # 持续跟踪
        self.persist_checkbox = QCheckBox("持续跟踪")
        self.persist_checkbox.setChecked(True)
        self.persist_checkbox.stateChanged.connect(self.on_config_changed)
        tracker_layout.addRow(self.persist_checkbox)

        layout.addWidget(tracker_group)

        # 轨迹显示组
        trail_group = QGroupBox("轨迹显示")
        trail_layout = QFormLayout(trail_group)

        # 显示轨迹
        self.show_trails_checkbox = QCheckBox("显示轨迹")
        self.show_trails_checkbox.setChecked(True)
        self.show_trails_checkbox.stateChanged.connect(self.on_trail_config_changed)
        trail_layout.addRow(self.show_trails_checkbox)

        # 轨迹长度
        self.trail_length_spinbox = QSpinBox()
        self.trail_length_spinbox.setRange(5, 100)
        self.trail_length_spinbox.setValue(30)
        self.trail_length_spinbox.valueChanged.connect(self.on_trail_config_changed)
        trail_layout.addRow("轨迹长度:", self.trail_length_spinbox)

        # 轨迹透明度
        self.trail_alpha_slider = QSlider(Qt.Horizontal)
        self.trail_alpha_slider.setRange(10, 100)
        self.trail_alpha_slider.setValue(80)
        self.trail_alpha_slider.valueChanged.connect(self.on_trail_config_changed)
        trail_layout.addRow("轨迹透明度:", self.trail_alpha_slider)

        layout.addWidget(trail_group)
        layout.addStretch()

        return widget
    
    def create_source_tab(self) -> QWidget:
        """创建输入源标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入源类型
        source_group = QGroupBox("输入源类型")
        source_layout = QVBoxLayout(source_group)
        
        # 摄像头
        camera_layout = QHBoxLayout()
        self.camera_radio = QCheckBox("摄像头")
        self.camera_radio.toggled.connect(self.on_source_type_changed)
        self.camera_combo = QComboBox()
        self.refresh_cameras_btn = QPushButton("刷新")
        self.refresh_cameras_btn.clicked.connect(self.refresh_cameras)
        camera_layout.addWidget(self.camera_radio)
        camera_layout.addWidget(self.camera_combo)
        camera_layout.addWidget(self.refresh_cameras_btn)
        camera_layout.addStretch()
        source_layout.addLayout(camera_layout)
        
        # 视频文件
        video_layout = QHBoxLayout()
        self.video_radio = QCheckBox("视频文件")
        self.video_radio.toggled.connect(self.on_source_type_changed)
        self.video_path_edit = QLineEdit()
        self.browse_video_btn = QPushButton("浏览...")
        self.browse_video_btn.clicked.connect(self.browse_video_file)
        video_layout.addWidget(self.video_radio)
        video_layout.addWidget(self.video_path_edit)
        video_layout.addWidget(self.browse_video_btn)
        source_layout.addLayout(video_layout)
        
        # 图像文件
        image_layout = QHBoxLayout()
        self.image_radio = QCheckBox("图像文件")
        self.image_radio.toggled.connect(self.on_source_type_changed)
        self.image_path_edit = QLineEdit()
        self.browse_image_btn = QPushButton("浏览...")
        self.browse_image_btn.clicked.connect(self.browse_image_file)
        image_layout.addWidget(self.image_radio)
        image_layout.addWidget(self.image_path_edit)
        image_layout.addWidget(self.browse_image_btn)
        source_layout.addLayout(image_layout)
        
        # RTSP流
        rtsp_layout = QHBoxLayout()
        self.rtsp_radio = QCheckBox("RTSP流")
        self.rtsp_radio.toggled.connect(self.on_source_type_changed)
        self.rtsp_url_edit = QLineEdit()
        self.rtsp_url_edit.setPlaceholderText("rtsp://username:password@ip:port/stream")
        rtsp_layout.addWidget(self.rtsp_radio)
        rtsp_layout.addWidget(self.rtsp_url_edit)
        source_layout.addLayout(rtsp_layout)
        
        layout.addWidget(source_group)
        layout.addStretch()

        return widget

    def create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()

        # 重置按钮
        reset_btn = QPushButton("重置配置")
        reset_btn.clicked.connect(self.reset_config)
        button_layout.addWidget(reset_btn)

        # 保存配置按钮
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        button_layout.addWidget(save_btn)

        # 加载配置按钮
        load_btn = QPushButton("加载配置")
        load_btn.clicked.connect(self.load_config_file)
        button_layout.addWidget(load_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def load_config(self):
        """加载配置"""
        # 加载模型配置
        supported_models = self.config_manager.get('model.supported_models', [])
        self.model_combo.clear()
        self.model_combo.addItems(supported_models)

        default_model = self.config_manager.get('model.default_model', 'yolo11n.pt')
        if default_model in supported_models:
            self.model_combo.setCurrentText(default_model)

        # 加载设备配置
        device = self.config_manager.get('detection.device', 'auto')
        self.device_combo.setCurrentText(device)

        # 加载检测参数
        conf = self.config_manager.get('detection.conf', 0.25)
        self.conf_slider.setValue(int(conf * 100))
        self.conf_spinbox.setValue(conf)

        iou = self.config_manager.get('detection.iou', 0.45)
        self.iou_slider.setValue(int(iou * 100))
        self.iou_spinbox.setValue(iou)

        self.max_det_spinbox.setValue(self.config_manager.get('detection.max_det', 1000))
        self.imgsz_combo.setCurrentText(str(self.config_manager.get('detection.imgsz', 640)))

        # 加载显示选项
        self.show_labels_checkbox.setChecked(self.config_manager.get('detection.show_labels', True))
        self.show_conf_checkbox.setChecked(self.config_manager.get('detection.show_conf', True))
        self.show_boxes_checkbox.setChecked(self.config_manager.get('detection.show_boxes', True))
        self.line_width_spinbox.setValue(self.config_manager.get('detection.line_width', 3))

        # 加载跟踪配置
        self.tracking_enabled_checkbox.setChecked(self.config_manager.get('tracking.enabled', False))
        self.tracker_combo.setCurrentText(self.config_manager.get('tracking.tracker', 'botsort.yaml'))
        self.track_history_spinbox.setValue(self.config_manager.get('tracking.track_history', 30))
        self.persist_checkbox.setChecked(self.config_manager.get('tracking.persist', True))

        # 加载输入源配置
        source_type = self.config_manager.get('source.type', 'camera')
        if source_type == 'camera':
            self.camera_radio.setChecked(True)
        elif source_type == 'video':
            self.video_radio.setChecked(True)
        elif source_type == 'image':
            self.image_radio.setChecked(True)
        elif source_type == 'rtsp':
            self.rtsp_radio.setChecked(True)

        # 刷新摄像头列表
        self.refresh_cameras()

    def on_model_changed(self):
        """模型改变事件"""
        model_name = self.model_combo.currentText()
        if model_name:
            self.config_manager.set('model.default_model', model_name)
            self.model_changed.emit(model_name)

    def on_config_changed(self):
        """配置改变事件"""
        config_updates = {}

        # 检测参数
        config_updates['detection.device'] = self.device_combo.currentText()
        config_updates['detection.half'] = self.half_checkbox.isChecked()
        config_updates['detection.max_det'] = self.max_det_spinbox.value()
        config_updates['detection.imgsz'] = int(self.imgsz_combo.currentText())
        config_updates['detection.augment'] = self.augment_checkbox.isChecked()
        config_updates['detection.show_labels'] = self.show_labels_checkbox.isChecked()
        config_updates['detection.show_conf'] = self.show_conf_checkbox.isChecked()
        config_updates['detection.show_boxes'] = self.show_boxes_checkbox.isChecked()
        config_updates['detection.line_width'] = self.line_width_spinbox.value()

        # 跟踪参数
        config_updates['tracking.tracker'] = self.tracker_combo.currentText()
        config_updates['tracking.track_history'] = self.track_history_spinbox.value()
        config_updates['tracking.persist'] = self.persist_checkbox.isChecked()

        self.config_manager.update(config_updates)
        self.config_changed.emit(config_updates)

    def on_slider_changed(self):
        """滑块改变事件"""
        sender = self.sender()
        if sender == self.conf_slider:
            value = sender.value() / 100.0
            self.conf_spinbox.setValue(value)
            self.config_manager.set('detection.conf', value)
        elif sender == self.iou_slider:
            value = sender.value() / 100.0
            self.iou_spinbox.setValue(value)
            self.config_manager.set('detection.iou', value)

        self.config_changed.emit({'detection.conf': self.conf_spinbox.value(),
                                'detection.iou': self.iou_spinbox.value()})

    def on_spinbox_changed(self):
        """数值框改变事件"""
        sender = self.sender()
        if sender == self.conf_spinbox:
            value = sender.value()
            self.conf_slider.setValue(int(value * 100))
            self.config_manager.set('detection.conf', value)
        elif sender == self.iou_spinbox:
            value = sender.value()
            self.iou_slider.setValue(int(value * 100))
            self.config_manager.set('detection.iou', value)

        self.config_changed.emit({'detection.conf': self.conf_spinbox.value(),
                                'detection.iou': self.iou_spinbox.value()})

    def on_tracking_enabled_changed(self):
        """跟踪启用状态改变"""
        enabled = self.tracking_enabled_checkbox.isChecked()
        self.config_manager.set('tracking.enabled', enabled)
        self.config_changed.emit({'tracking.enabled': enabled})

    def on_trail_config_changed(self):
        """轨迹配置改变事件"""
        trail_config = {
            'show_trails': self.show_trails_checkbox.isChecked(),
            'trail_length': self.trail_length_spinbox.value(),
            'trail_alpha': self.trail_alpha_slider.value() / 100.0
        }

        # 发送轨迹配置信号
        self.config_changed.emit({'trail_config': trail_config})

    def on_source_type_changed(self):
        """输入源类型改变"""
        source_config = {}

        if self.camera_radio.isChecked():
            source_config['type'] = 'camera'
            source_config['camera_id'] = self.camera_combo.currentData() or 0
        elif self.video_radio.isChecked():
            source_config['type'] = 'video'
            source_config['video_path'] = self.video_path_edit.text()
        elif self.image_radio.isChecked():
            source_config['type'] = 'image'
            source_config['image_path'] = self.image_path_edit.text()
        elif self.rtsp_radio.isChecked():
            source_config['type'] = 'rtsp'
            source_config['rtsp_url'] = self.rtsp_url_edit.text()

        self.config_manager.update({f'source.{k}': v for k, v in source_config.items()})
        self.source_changed.emit(source_config)

    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模型文件", "",
            "模型文件 (*.pt *.onnx *.engine);;所有文件 (*)"
        )
        if file_path:
            self.model_path_edit.setText(file_path)
            self.model_combo.setCurrentText(file_path)

    def browse_video_file(self):
        """浏览视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv);;所有文件 (*)"
        )
        if file_path:
            self.video_path_edit.setText(file_path)
            self.video_radio.setChecked(True)
            self.on_source_type_changed()

    def browse_image_file(self):
        """浏览图像文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.webp);;所有文件 (*)"
        )
        if file_path:
            self.image_path_edit.setText(file_path)
            self.image_radio.setChecked(True)
            self.on_source_type_changed()

    def refresh_cameras(self):
        """刷新摄像头列表"""
        self.camera_combo.clear()
        cameras = get_available_cameras()
        for camera_id in cameras:
            self.camera_combo.addItem(f"摄像头 {camera_id}", camera_id)

        if cameras:
            camera_id = self.config_manager.get('source.camera_id', 0)
            index = self.camera_combo.findData(camera_id)
            if index >= 0:
                self.camera_combo.setCurrentIndex(index)

    def reset_config(self):
        """重置配置"""
        reply = QMessageBox.question(self, "确认", "确定要重置所有配置到默认值吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.config_manager.reset_to_default()
            self.load_config()
            self.config_changed.emit({})

    def save_config(self):
        """保存配置"""
        if self.config_manager.save_config():
            QMessageBox.information(self, "成功", "配置保存成功！")
        else:
            QMessageBox.warning(self, "错误", "配置保存失败！")

    def load_config_file(self):
        """加载配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择配置文件", "", "YAML文件 (*.yaml *.yml);;所有文件 (*)"
        )
        if file_path:
            if self.config_manager.import_config(file_path):
                self.load_config()
                self.config_changed.emit({})
                QMessageBox.information(self, "成功", "配置加载成功！")
            else:
                QMessageBox.warning(self, "错误", "配置加载失败！")

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            'model': self.model_combo.currentText(),
            'device': self.device_combo.currentText(),
            'conf': self.conf_spinbox.value(),
            'iou': self.iou_spinbox.value(),
            'max_det': self.max_det_spinbox.value(),
            'imgsz': int(self.imgsz_combo.currentText()),
            'tracking_enabled': self.tracking_enabled_checkbox.isChecked(),
            'tracker': self.tracker_combo.currentText(),
            'track_history': self.track_history_spinbox.value()
        }
