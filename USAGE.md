# YOLO全能检测系统使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
python run.py
```
或者
```bash
python main.py
```

## 界面介绍

### 主界面布局
- **左侧**: 控制面板 - 配置模型、检测参数、跟踪参数和输入源
- **中间**: 视频显示区域 - 实时显示检测和跟踪结果
- **右侧**: 结果面板 - 显示检测统计、跟踪信息和系统日志

### 菜单栏功能
- **文件**: 打开文件、保存结果、退出程序
- **工具**: 模型管理、配置管理
- **帮助**: 关于信息

### 工具栏功能
- **开始检测**: 启动检测/跟踪
- **停止检测**: 停止当前检测
- **截图**: 保存当前帧
- **录制**: 录制检测视频（待实现）

## 详细使用步骤

### 第一步：选择和配置模型

1. 在左侧控制面板点击"模型配置"标签页
2. 从下拉菜单选择预训练模型，或点击"浏览"选择自定义模型
3. 选择计算设备（auto/cpu/cuda）
4. 可选择启用半精度推理以提高速度

**支持的模型类型：**
- 目标检测：yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt
- 实例分割：yolo11n-seg.pt, yolo11s-seg.pt, 等
- 姿态估计：yolo11n-pose.pt, yolo11s-pose.pt, 等
- 图像分类：yolo11n-cls.pt, yolo11s-cls.pt, 等
- 有向边界框：yolo11n-obb.pt, yolo11s-obb.pt, 等

### 第二步：调整检测参数

1. 点击"检测参数"标签页
2. 调整关键参数：
   - **置信度阈值**: 控制检测的敏感度（0.0-1.0）
   - **IoU阈值**: 控制重叠检测的过滤（0.0-1.0）
   - **最大检测数**: 每帧最多检测的目标数量
   - **图像尺寸**: 输入图像的处理尺寸
3. 配置显示选项：
   - 显示标签、置信度、边界框
   - 调整线条粗细

### 第三步：配置目标跟踪（可选）

1. 点击"跟踪参数"标签页
2. 勾选"启用目标跟踪"
3. 选择跟踪算法：
   - **BoT-SORT**: 适合复杂场景，支持重新识别
   - **ByteTrack**: 简单高效，适合实时应用
4. 调整轨迹历史长度（建议10-50）

### 第四步：选择输入源

1. 点击"输入源"标签页
2. 选择输入类型：

**摄像头输入：**
- 勾选"摄像头"
- 从下拉菜单选择摄像头设备
- 点击"刷新"更新可用摄像头列表

**视频文件：**
- 勾选"视频文件"
- 点击"浏览"选择视频文件
- 支持格式：mp4, avi, mov, mkv, flv, wmv

**图像文件：**
- 勾选"图像文件"
- 点击"浏览"选择图像文件
- 支持格式：jpg, jpeg, png, bmp, tiff, webp

**RTSP流：**
- 勾选"RTSP流"
- 输入RTSP地址，格式如：`rtsp://username:password@ip:port/stream`

### 第五步：开始检测

1. 确认所有配置正确
2. 点击工具栏"开始检测"按钮
3. 在中间区域查看实时检测结果
4. 在右侧面板查看详细统计信息

## 结果查看和分析

### 实时信息面板
- **基本信息**: 显示当前状态、模型信息、输入源信息
- **实时统计**: FPS、检测数量、跟踪数量、处理时间
- **系统日志**: 显示操作日志和错误信息

### 检测结果面板
- 表格显示所有检测到的目标
- 包含类别、置信度、边界框坐标信息
- 支持按列排序

### 跟踪结果面板
- **跟踪统计**: 活跃轨迹数、总轨迹数、平均轨迹长度
- **跟踪详情**: 每个跟踪目标的详细信息
- 包含跟踪ID、类别、置信度、位置、轨迹长度

### 性能监控面板
- CPU、内存、GPU使用率
- 性能历史记录
- 帮助优化系统性能

## 高级功能

### 配置管理
- **保存配置**: 保存当前所有参数设置
- **加载配置**: 从文件加载配置
- **重置配置**: 恢复默认设置
- **导入/导出**: 与其他用户分享配置

### 结果导出
- **截图**: 保存当前检测画面
- **数据导出**: 导出跟踪数据为JSON格式
- **批量处理**: 处理多个文件（待实现）

### 自定义模型
1. 训练自己的YOLO模型
2. 将.pt文件放在项目目录
3. 在模型配置中选择自定义模型
4. 系统自动识别模型类型和类别

## 性能优化建议

### 提高检测速度
1. 使用较小的模型（如yolo11n.pt）
2. 降低输入图像尺寸
3. 启用GPU加速
4. 启用半精度推理
5. 提高置信度阈值

### 提高检测精度
1. 使用较大的模型（如yolo11x.pt）
2. 增加输入图像尺寸
3. 降低置信度阈值
4. 启用数据增强

### 优化跟踪效果
1. 根据场景选择合适的跟踪算法
2. 调整跟踪器参数
3. 增加轨迹历史长度
4. 提高检测置信度

## 常见问题解决

### 模型加载失败
- 检查模型文件是否存在
- 确认模型文件格式正确（.pt）
- 检查网络连接（自动下载模型时）

### 检测速度慢
- 降低图像尺寸
- 使用更小的模型
- 启用GPU加速
- 关闭不必要的显示选项

### 摄像头无法打开
- 检查摄像头是否被其他程序占用
- 尝试不同的摄像头ID
- 检查摄像头驱动是否正常

### 跟踪效果不佳
- 提高检测置信度
- 调整跟踪器参数
- 尝试不同的跟踪算法
- 确保光照条件良好

## 技术支持

如遇到问题，请：
1. 查看系统日志面板的错误信息
2. 检查配置参数是否正确
3. 参考本使用指南
4. 提交Issue到项目仓库

## 更新和维护

- 定期更新Ultralytics库获取最新模型
- 关注项目更新获取新功能
- 备份重要的配置文件
- 定期清理输出目录
