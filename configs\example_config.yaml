# YOLO检测系统配置示例
# 这是一个配置示例文件，展示了所有可配置的参数

# 模型配置
model:
  # 默认使用的模型
  default_model: "yolo11n.pt"
  
  # 支持的模型列表（可以添加自定义模型）
  supported_models:
    # YOLO11 检测模型
    - "yolo11n.pt"
    - "yolo11s.pt" 
    - "yolo11m.pt"
    - "yolo11l.pt"
    - "yolo11x.pt"
    
    # YOLO11 分割模型
    - "yolo11n-seg.pt"
    - "yolo11s-seg.pt"
    - "yolo11m-seg.pt"
    - "yolo11l-seg.pt"
    - "yolo11x-seg.pt"
    
    # YOLO11 姿态估计模型
    - "yolo11n-pose.pt"
    - "yolo11s-pose.pt"
    - "yolo11m-pose.pt"
    - "yolo11l-pose.pt"
    - "yolo11x-pose.pt"
    
    # YOLO11 分类模型
    - "yolo11n-cls.pt"
    - "yolo11s-cls.pt"
    - "yolo11m-cls.pt"
    - "yolo11l-cls.pt"
    - "yolo11x-cls.pt"
    
    # YOLO11 OBB检测模型
    - "yolo11n-obb.pt"
    - "yolo11s-obb.pt"
    - "yolo11m-obb.pt"
    - "yolo11l-obb.pt"
    - "yolo11x-obb.pt"
    
    # YOLOv8 模型
    - "yolov8n.pt"
    - "yolov8s.pt"
    - "yolov8m.pt"
    - "yolov8l.pt"
    - "yolov8x.pt"
    
    # YOLOv5 模型
    - "yolov5n.pt"
    - "yolov5s.pt"
    - "yolov5m.pt"
    - "yolov5l.pt"
    - "yolov5x.pt"

# 检测参数配置
detection:
  # 置信度阈值 (0.0-1.0)
  conf: 0.25
  
  # IoU阈值 (0.0-1.0)
  iou: 0.45
  
  # 最大检测数量
  max_det: 1000
  
  # 输入图像尺寸
  imgsz: 640
  
  # 计算设备 (auto, cpu, cuda, 0, 1, 2, ...)
  device: "auto"
  
  # 是否使用半精度推理
  half: false
  
  # 是否启用数据增强
  augment: false
  
  # 是否可视化特征图
  visualize: false
  
  # 是否保存结果
  save: false
  
  # 是否保存置信度
  save_conf: false
  
  # 是否保存文本结果
  save_txt: false
  
  # 是否保存裁剪图像
  save_crop: false
  
  # 显示选项
  show_labels: true
  show_conf: true
  show_boxes: true
  
  # 线条粗细
  line_width: 3

# 跟踪参数配置
tracking:
  # 是否启用跟踪
  enabled: false
  
  # 跟踪器配置文件
  tracker: "botsort.yaml"
  
  # 是否持续跟踪
  persist: true
  
  # 轨迹历史长度
  track_history: 30

# 输入源配置
source:
  # 输入源类型 (camera, video, image, rtsp)
  type: "camera"
  
  # 摄像头设备ID
  camera_id: 0
  
  # 视频文件路径
  video_path: ""
  
  # 图像文件路径
  image_path: ""
  
  # RTSP流地址
  rtsp_url: ""
  
  # 批处理目录
  batch_dir: ""

# 输出配置
output:
  # 输出目录
  save_dir: "runs/detect"
  
  # 输出格式
  format: "mp4"
  
  # 视频编码器
  fourcc: "mp4v"
  
  # 输出帧率
  fps: 30
  
  # 视频质量
  quality: 95

# 用户界面配置
ui:
  # 窗口标题
  title: "YOLO全能检测系统"
  
  # 窗口大小 [宽度, 高度]
  window_size: [1400, 900]
  
  # 主题 (dark, light)
  theme: "dark"
  
  # 语言
  language: "zh_CN"
  
  # 是否自动保存配置
  auto_save: true

# 性能配置
performance:
  # 是否启用多线程处理
  multi_threading: true
  
  # 最大线程数
  max_threads: 4
  
  # 缓存大小
  cache_size: 100
  
  # GPU内存使用比例
  gpu_memory_fraction: 0.8

# 高级配置
advanced:
  # 调试模式
  debug: false
  
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  log_level: "INFO"
  
  # 是否显示详细信息
  verbose: false
  
  # 自动下载模型
  auto_download: true
