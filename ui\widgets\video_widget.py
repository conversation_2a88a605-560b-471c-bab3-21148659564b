"""
视频显示组件
用于显示视频流和检测结果
"""

import cv2
import numpy as np
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout, QHBoxLayout, QSizePolicy
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QImage, QPixmap, QPainter, QPen, QFont
from typing import Optional, Tuple


class VideoDisplayWidget(QWidget):
    """视频显示组件"""
    
    # 信号定义
    frame_clicked = Signal(int, int)  # 点击位置信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
        # 显示相关
        self.current_frame = None
        self.display_size = (640, 480)
        self.keep_aspect_ratio = True
        self.show_fps = True
        self.show_info = True
        
        # 性能信息
        self.fps_text = "FPS: 0"
        self.info_text = ""
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 视频显示标签
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                border: 2px solid #555;
                border-radius: 5px;
            }
        """)
        self.video_label.setText("等待视频输入...")
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 启用鼠标事件
        self.video_label.mousePressEvent = self.on_mouse_press
        
        layout.addWidget(self.video_label)
    
    def update_frame(self, frame: np.ndarray, fps: float = 0, info: str = ""):
        """
        更新显示帧
        
        Args:
            frame: 视频帧
            fps: 帧率
            info: 信息文本
        """
        if frame is None:
            return
        
        self.current_frame = frame.copy()
        self.fps_text = f"FPS: {fps:.1f}"
        self.info_text = info
        
        # 在帧上绘制信息
        display_frame = self._draw_overlay(frame)
        
        # 转换为Qt图像并显示
        qt_image = self._numpy_to_qimage(display_frame)
        if qt_image:
            # 调整大小
            label_size = self.video_label.size()
            scaled_pixmap = QPixmap.fromImage(qt_image).scaled(
                label_size, Qt.KeepAspectRatio if self.keep_aspect_ratio else Qt.IgnoreAspectRatio,
                Qt.SmoothTransformation
            )
            self.video_label.setPixmap(scaled_pixmap)
    
    def _draw_overlay(self, frame: np.ndarray) -> np.ndarray:
        """
        在帧上绘制覆盖信息
        
        Args:
            frame: 输入帧
            
        Returns:
            绘制后的帧
        """
        overlay_frame = frame.copy()
        h, w = overlay_frame.shape[:2]
        
        # 绘制FPS
        if self.show_fps:
            cv2.putText(overlay_frame, self.fps_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制信息文本
        if self.show_info and self.info_text:
            lines = self.info_text.split('\n')
            for i, line in enumerate(lines):
                y_pos = h - 30 - (len(lines) - 1 - i) * 25
                cv2.putText(overlay_frame, line, (10, y_pos),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return overlay_frame
    
    def _numpy_to_qimage(self, frame: np.ndarray) -> Optional[QImage]:
        """
        将numpy数组转换为QImage
        
        Args:
            frame: numpy图像数组
            
        Returns:
            QImage对象
        """
        try:
            if len(frame.shape) == 3:
                h, w, ch = frame.shape
                if ch == 3:
                    # BGR转RGB
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    return QImage(rgb_frame.data, w, h, ch * w, QImage.Format_RGB888)
                elif ch == 4:
                    return QImage(frame.data, w, h, ch * w, QImage.Format_RGBA8888)
            elif len(frame.shape) == 2:
                h, w = frame.shape
                return QImage(frame.data, w, h, w, QImage.Format_Grayscale8)
        except Exception as e:
            print(f"图像转换失败: {e}")
        
        return None
    
    def on_mouse_press(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton and self.current_frame is not None:
            # 获取点击位置
            label_size = self.video_label.size()
            click_x = event.pos().x()
            click_y = event.pos().y()
            
            # 转换为原始图像坐标
            if self.current_frame is not None:
                h, w = self.current_frame.shape[:2]
                
                if self.keep_aspect_ratio:
                    # 计算缩放比例和偏移
                    scale_x = label_size.width() / w
                    scale_y = label_size.height() / h
                    scale = min(scale_x, scale_y)
                    
                    scaled_w = int(w * scale)
                    scaled_h = int(h * scale)
                    
                    offset_x = (label_size.width() - scaled_w) // 2
                    offset_y = (label_size.height() - scaled_h) // 2
                    
                    # 调整点击坐标
                    adjusted_x = click_x - offset_x
                    adjusted_y = click_y - offset_y
                    
                    if 0 <= adjusted_x <= scaled_w and 0 <= adjusted_y <= scaled_h:
                        orig_x = int(adjusted_x / scale)
                        orig_y = int(adjusted_y / scale)
                        self.frame_clicked.emit(orig_x, orig_y)
                else:
                    orig_x = int(click_x * w / label_size.width())
                    orig_y = int(click_y * h / label_size.height())
                    self.frame_clicked.emit(orig_x, orig_y)
    
    def clear_display(self):
        """清空显示"""
        self.video_label.clear()
        self.video_label.setText("等待视频输入...")
        self.current_frame = None
    
    def save_current_frame(self, file_path: str) -> bool:
        """
        保存当前帧
        
        Args:
            file_path: 保存路径
            
        Returns:
            是否保存成功
        """
        if self.current_frame is not None:
            try:
                cv2.imwrite(file_path, self.current_frame)
                return True
            except Exception as e:
                print(f"保存帧失败: {e}")
        return False
    
    def set_display_options(self, show_fps: bool = True, show_info: bool = True,
                           keep_aspect_ratio: bool = True):
        """
        设置显示选项
        
        Args:
            show_fps: 是否显示FPS
            show_info: 是否显示信息
            keep_aspect_ratio: 是否保持宽高比
        """
        self.show_fps = show_fps
        self.show_info = show_info
        self.keep_aspect_ratio = keep_aspect_ratio
    
    def get_display_size(self) -> Tuple[int, int]:
        """
        获取显示尺寸
        
        Returns:
            显示尺寸元组
        """
        size = self.video_label.size()
        return (size.width(), size.height())
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 如果有当前帧，重新显示以适应新尺寸
        if self.current_frame is not None:
            self.update_frame(self.current_frame, 
                            float(self.fps_text.split(': ')[1]) if ': ' in self.fps_text else 0,
                            self.info_text)
